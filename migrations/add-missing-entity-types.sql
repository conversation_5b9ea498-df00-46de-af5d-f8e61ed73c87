-- Migration: Add Missing Entity Types
-- Description: Adds the missing entity types to enable all enhanced frontend forms
-- Date: 2025-06-22

-- Add missing entity types to support all frontend forms
INSERT INTO entity_types (id, name, slug, description, created_at, updated_at) VALUES 
-- HIGH PRIORITY: Core Business Value
(gen_random_uuid(), 'Course', 'course', 'Educational courses and training programs for AI and technology', NOW(), NOW()),
(gen_random_uuid(), 'Agency', 'agency', 'AI consulting agencies and professional service providers', NOW(), NOW()),
(gen_random_uuid(), 'Hardware', 'hardware', 'AI hardware, GPUs, and computing infrastructure', NOW(), NOW()),
(gen_random_uuid(), 'Software', 'software', 'General software applications and development tools', NOW(), NOW()),

-- MEDIUM PRIORITY: Community & Knowledge  
(gen_random_uuid(), 'Research Paper', 'research-paper', 'Academic papers and research publications in AI', NOW(), NOW()),
(gen_random_uuid(), 'Job', 'job', 'AI and technology job opportunities and career positions', NOW(), NOW()),
(gen_random_uuid(), 'Event', 'event', 'AI conferences, workshops, meetups, and industry events', NOW(), NOW()),
(gen_random_uuid(), 'Podcast', 'podcast', 'AI and technology podcasts and audio content', NOW(), NOW()),
(gen_random_uuid(), 'Community', 'community', 'AI communities, forums, and discussion groups', NOW(), NOW()),
(gen_random_uuid(), 'Grant', 'grant', 'Research grants and funding opportunities for AI projects', NOW(), NOW()),
(gen_random_uuid(), 'Newsletter', 'newsletter', 'AI and technology newsletters and publications', NOW(), NOW())

-- Handle conflicts if entity types already exist
ON CONFLICT (name) DO NOTHING;

-- Verify the insertion
SELECT 
    name, 
    slug, 
    description,
    created_at
FROM entity_types 
WHERE name IN (
    'Course', 'Agency', 'Hardware', 'Software', 
    'Research Paper', 'Job', 'Event', 'Podcast', 
    'Community', 'Grant', 'Newsletter'
)
ORDER BY name;
