#!/usr/bin/env node

/**
 * Check what tables actually exist in the current database
 */

const { PrismaClient } = require('../generated/prisma');

const prisma = new PrismaClient();

async function checkCurrentDatabase() {
  console.log('🔍 Checking current database tables...\n');
  
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful\n');
    
    // Query to get all table names
    const result = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `;
    
    console.log(`📊 Found ${result.length} tables in current database:`);
    result.forEach((row, index) => {
      console.log(`   ${index + 1}. ${row.table_name}`);
    });
    
    // Check specific tables we're interested in
    const checkTables = [
      'app_settings', 
      'user_preferences', 
      'tool_requests', 
      'user_submitted_tools', 
      'profile_activities'
    ];
    
    console.log('\n🔍 Checking specific tables:');
    const existingTables = result.map(row => row.table_name);
    
    checkTables.forEach(table => {
      const exists = existingTables.includes(table);
      console.log(`   ${exists ? '✅' : '❌'} ${table}`);
    });
    
    // Check if any tables from SQL dump are missing
    const sqlDumpTables = [
      '_prisma_migrations', 'badge_types', 'categories', 'entities', 'entity_badges',
      'entity_categories', 'entity_details_agency', 'entity_details_book', 'entity_details_bounty',
      'entity_details_community', 'entity_details_content_creator', 'entity_details_course',
      'entity_details_dataset', 'entity_details_event', 'entity_details_grant', 'entity_details_hardware',
      'entity_details_investor', 'entity_details_job', 'entity_details_model', 'entity_details_news',
      'entity_details_newsletter', 'entity_details_platform', 'entity_details_podcast',
      'entity_details_project_reference', 'entity_details_research_paper', 'entity_details_service_provider',
      'entity_details_software', 'entity_details_tool', 'entity_features', 'entity_tags',
      'entity_types', 'features', 'review_votes', 'reviews', 'tags', 'user_activity_logs',
      'user_badges', 'user_followed_categories', 'user_followed_tags', 'user_notification_settings',
      'user_saved_entities', 'users'
    ];
    
    console.log('\n🔍 Checking SQL dump tables against current database:');
    const missingFromDb = sqlDumpTables.filter(table => !existingTables.includes(table));
    const extraInDb = existingTables.filter(table => !sqlDumpTables.includes(table));
    
    if (missingFromDb.length > 0) {
      console.log(`\n❌ Tables in SQL dump but missing from database (${missingFromDb.length}):`);
      missingFromDb.forEach(table => console.log(`   • ${table}`));
    }
    
    if (extraInDb.length > 0) {
      console.log(`\n⚠️  Tables in database but not in SQL dump (${extraInDb.length}):`);
      extraInDb.forEach(table => console.log(`   • ${table}`));
    }
    
    if (missingFromDb.length === 0 && extraInDb.length === 0) {
      console.log('\n✅ Perfect match between SQL dump and current database!');
    }
    
    return {
      currentTables: existingTables,
      missingFromDb,
      extraInDb
    };
    
  } catch (error) {
    console.error('❌ Error checking database:', error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkCurrentDatabase()
  .then((result) => {
    console.log('\n✨ Database check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Database check failed:', error);
    process.exit(1);
  });
