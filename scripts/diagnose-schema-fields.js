#!/usr/bin/env node

/**
 * Diagnose Schema Field Mismatches
 * Check actual database schema field names vs our filter field names
 */

const { PrismaClient } = require('../generated/prisma');

const prisma = new PrismaClient();

async function checkDetailTableSchemas() {
  console.log('🔍 Checking detail table schemas...\n');
  
  try {
    await prisma.$connect();
    
    // Check each detail table structure
    const detailTables = [
      'entity_details_course',
      'entity_details_job', 
      'entity_details_hardware',
      'entity_details_event'
    ];
    
    for (const tableName of detailTables) {
      console.log(`📋 Checking ${tableName}:`);
      
      try {
        const columns = await prisma.$queryRaw`
          SELECT column_name, data_type, is_nullable
          FROM information_schema.columns 
          WHERE table_name = ${tableName}
          AND table_schema = 'public'
          ORDER BY ordinal_position;
        `;
        
        console.log(`   Columns (${columns.length}):`);
        columns.forEach(col => {
          console.log(`      • ${col.column_name} (${col.data_type}${col.is_nullable === 'YES' ? ', nullable' : ''})`);
        });
        console.log('');
        
      } catch (error) {
        console.log(`   ❌ Error querying ${tableName}: ${error.message}\n`);
      }
    }
    
  } catch (error) {
    console.error('❌ Schema check failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

async function checkEnumValues() {
  console.log('🔍 Checking enum values...\n');
  
  try {
    await prisma.$connect();
    
    // Check EntityStatus enum
    console.log('📋 Checking EntityStatus enum:');
    const statusEnums = await prisma.$queryRaw`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'EntityStatus'
      );
    `;
    
    console.log('   Available values:');
    statusEnums.forEach(status => {
      console.log(`      • ${status.enumlabel}`);
    });
    console.log('');
    
    // Check SkillLevel enum
    console.log('📋 Checking SkillLevel enum:');
    const skillEnums = await prisma.$queryRaw`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'SkillLevel'
      );
    `;
    
    console.log('   Available values:');
    skillEnums.forEach(skill => {
      console.log(`      • ${skill.enumlabel}`);
    });
    console.log('');
    
  } catch (error) {
    console.error('❌ Enum check failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

async function checkExistingEntities() {
  console.log('🔍 Checking existing entities by type...\n');
  
  try {
    await prisma.$connect();
    
    // Get entity type counts
    const entityCounts = await prisma.$queryRaw`
      SELECT et.name, et.slug, COUNT(e.id) as entity_count
      FROM entity_types et
      LEFT JOIN entities e ON et.id = e.entity_type_id
      GROUP BY et.id, et.name, et.slug
      ORDER BY entity_count DESC;
    `;
    
    console.log('📊 Entity counts by type:');
    entityCounts.forEach(row => {
      console.log(`   • ${row.name} (${row.slug}): ${row.entity_count} entities`);
    });
    console.log('');
    
    // Check if we have any entities with detail records
    console.log('📋 Checking entities with detail records:');
    
    const courseDetails = await prisma.entityDetailsCourse.count();
    const jobDetails = await prisma.entityDetailsJob.count();
    const hardwareDetails = await prisma.entityDetailsHardware.count();
    const eventDetails = await prisma.entityDetailsEvent.count();
    
    console.log(`   • Course details: ${courseDetails}`);
    console.log(`   • Job details: ${jobDetails}`);
    console.log(`   • Hardware details: ${hardwareDetails}`);
    console.log(`   • Event details: ${eventDetails}`);
    console.log('');
    
  } catch (error) {
    console.error('❌ Entity check failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

async function testBasicQueries() {
  console.log('🔍 Testing basic queries...\n');
  
  try {
    await prisma.$connect();
    
    // Test basic entity query
    console.log('📋 Testing basic entity query:');
    const entities = await prisma.entity.findMany({
      take: 3,
      include: {
        entityType: true
      }
    });
    
    console.log(`   Found ${entities.length} entities:`);
    entities.forEach(entity => {
      console.log(`      • ${entity.name} (${entity.entityType.name})`);
    });
    console.log('');
    
    // Test status filtering
    console.log('📋 Testing status filtering:');
    try {
      const approvedEntities = await prisma.entity.findMany({
        where: {
          status: 'APPROVED'
        },
        take: 2
      });
      console.log(`   Found ${approvedEntities.length} approved entities`);
    } catch (error) {
      console.log(`   ❌ Status filter error: ${error.message}`);
    }
    console.log('');
    
  } catch (error) {
    console.error('❌ Query test failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

async function runDiagnosis() {
  console.log('🚀 SCHEMA FIELD DIAGNOSIS');
  console.log('=' .repeat(50));
  
  await checkDetailTableSchemas();
  await checkEnumValues();
  await checkExistingEntities();
  await testBasicQueries();
  
  console.log('🎯 DIAGNOSIS SUMMARY');
  console.log('=' .repeat(50));
  console.log('✅ Schema field analysis complete');
  console.log('✅ Enum value analysis complete');
  console.log('✅ Entity count analysis complete');
  console.log('✅ Basic query testing complete');
  console.log('');
  console.log('📋 Next steps:');
  console.log('   1. Fix field name mismatches in filter methods');
  console.log('   2. Create test data for new entity types');
  console.log('   3. Update enum value mappings');
  console.log('   4. Re-run comprehensive tests');
}

// Run the diagnosis
runDiagnosis()
  .then(() => {
    console.log('\n✨ Diagnosis completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Diagnosis failed:', error);
    process.exit(1);
  });
