#!/usr/bin/env node

/**
 * Create Test Data for Enhanced Filtering
 * Creates sample entities for Course, Job, Hardware, and Event types
 */

const { PrismaClient } = require('../generated/prisma');

const prisma = new PrismaClient();

async function createTestEntities() {
  console.log('🚀 Creating test data for enhanced filtering...\n');
  
  try {
    await prisma.$connect();

    // Get or create a test user for submitting entities
    let testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          displayName: 'Test User',
          username: 'testuser',
          authUserId: '550e8400-e29b-41d4-a716-446655440000'
        }
      });
      console.log('✅ Created test user');
    } else {
      console.log('✅ Found existing test user');
    }

    // Get entity type IDs
    const entityTypes = await prisma.entityType.findMany({
      where: {
        slug: { in: ['course', 'job', 'hardware', 'event'] }
      }
    });

    const entityTypeMap = {};
    entityTypes.forEach(et => {
      entityTypeMap[et.slug] = et.id;
    });

    console.log('📋 Found entity types:');
    Object.keys(entityTypeMap).forEach(slug => {
      console.log(`   • ${slug}: ${entityTypeMap[slug]}`);
    });
    console.log('');
    
    // Create Course entities
    if (entityTypeMap.course) {
      console.log('🎓 Creating Course entities...');
      
      const course1 = await prisma.entity.create({
        data: {
          name: 'AI Fundamentals for Beginners',
          slug: 'ai-fundamentals-beginners',
          websiteUrl: 'https://example.com/ai-course-1',
          shortDescription: 'Learn the basics of artificial intelligence',
          description: 'A comprehensive introduction to AI concepts, perfect for beginners.',
          status: 'ACTIVE',
          entityType: {
            connect: { id: entityTypeMap.course }
          },
          submitter: {
            connect: { id: testUser.id }
          },
          entityDetailsCourse: {
            create: {
              instructorName: 'Dr. Sarah Johnson',
              durationText: '8 weeks',
              skillLevel: 'BEGINNER',
              prerequisites: 'Basic programming knowledge',
              syllabusUrl: 'https://example.com/syllabus-1',
              enrollmentCount: 1250,
              certificateAvailable: true
            }
          }
        }
      });
      
      const course2 = await prisma.entity.create({
        data: {
          name: 'Advanced Machine Learning',
          slug: 'advanced-machine-learning',
          websiteUrl: 'https://example.com/ai-course-2',
          shortDescription: 'Deep dive into ML algorithms',
          description: 'Advanced course covering neural networks, deep learning, and more.',
          status: 'ACTIVE',
          entityType: {
            connect: { id: entityTypeMap.course }
          },
          submitter: {
            connect: { id: testUser.id }
          },
          entityDetailsCourse: {
            create: {
              instructorName: 'Prof. Michael Chen',
              durationText: '12 weeks',
              skillLevel: 'ADVANCED',
              prerequisites: 'Linear algebra, Python programming, statistics',
              syllabusUrl: 'https://example.com/syllabus-2',
              enrollmentCount: 850,
              certificateAvailable: true
            }
          }
        }
      });
      
      console.log(`   ✅ Created ${course1.name}`);
      console.log(`   ✅ Created ${course2.name}`);
    }
    
    // Create Job entities
    if (entityTypeMap.job) {
      console.log('\n💼 Creating Job entities...');
      
      const job1 = await prisma.entity.create({
        data: {
          name: 'Senior AI Engineer - Remote',
          slug: 'senior-ai-engineer-remote',
          websiteUrl: 'https://example.com/job-1',
          shortDescription: 'Join our AI team to build cutting-edge solutions',
          description: 'We are looking for a senior AI engineer to lead our machine learning initiatives.',
          entityType: {
            connect: { id: entityTypeMap.job }
          },
          status: 'ACTIVE',
          submitter: {
            connect: { id: testUser.id }
          },
          entityDetailsJob: {
            create: {
              companyName: 'TechCorp AI',
              jobType: 'Full-time',
              experienceLevel: 'Senior',
              location: 'Remote',
              salaryMin: 120000,
              salaryMax: 180000,
              applicationUrl: 'https://example.com/apply-1',
              isRemote: true,
              keyResponsibilities: ['Develop ML models', 'Lead AI projects', 'Mentor junior engineers'],
              requiredSkills: ['Python', 'TensorFlow', 'PyTorch', 'AWS']
            }
          }
        }
      });
      
      const job2 = await prisma.entity.create({
        data: {
          name: 'ML Engineer - San Francisco',
          slug: 'ml-engineer-san-francisco',
          websiteUrl: 'https://example.com/job-2',
          shortDescription: 'Build scalable ML systems in our SF office',
          description: 'Join our team to build production ML systems at scale.',
          entityType: {
            connect: { id: entityTypeMap.job }
          },
          status: 'ACTIVE',
          submitter: {
            connect: { id: testUser.id }
          },
          entityDetailsJob: {
            create: {
              companyName: 'AI Startup Inc',
              jobType: 'Full-time',
              experienceLevel: 'Mid',
              location: 'San Francisco, CA',
              salaryMin: 100000,
              salaryMax: 140000,
              applicationUrl: 'https://example.com/apply-2',
              isRemote: false,
              keyResponsibilities: ['Build ML pipelines', 'Deploy models', 'Optimize performance'],
              requiredSkills: ['Python', 'Kubernetes', 'MLflow', 'GCP']
            }
          }
        }
      });
      
      console.log(`   ✅ Created ${job1.name}`);
      console.log(`   ✅ Created ${job2.name}`);
    }
    
    // Create Hardware entities
    if (entityTypeMap.hardware) {
      console.log('\n🖥️ Creating Hardware entities...');
      
      const hardware1 = await prisma.entity.create({
        data: {
          name: 'NVIDIA RTX 4090 AI Workstation',
          slug: 'nvidia-rtx-4090-workstation',
          websiteUrl: 'https://example.com/hardware-1',
          shortDescription: 'High-performance GPU for AI workloads',
          description: 'Professional workstation optimized for AI and machine learning tasks.',
          entityType: {
            connect: { id: entityTypeMap.hardware }
          },
          status: 'ACTIVE',
          submitter: {
            connect: { id: testUser.id }
          },
          entityDetailsHardware: {
            create: {
              gpu: 'NVIDIA GeForce RTX 4090',
              processor: 'Intel Core i9-13900K',
              memory: '64GB DDR5',
              storage: '2TB NVMe SSD',
              powerConsumption: '450W',
              price: '$3,999',
              availability: 'In Stock',
              useCases: ['Deep Learning', 'AI Training', 'Computer Vision']
            }
          }
        }
      });
      
      const hardware2 = await prisma.entity.create({
        data: {
          name: 'Apple Mac Studio M2 Ultra',
          slug: 'apple-mac-studio-m2-ultra',
          websiteUrl: 'https://example.com/hardware-2',
          shortDescription: 'Apple Silicon for AI development',
          description: 'Powerful Mac Studio with M2 Ultra chip for AI and ML development.',
          entityType: {
            connect: { id: entityTypeMap.hardware }
          },
          status: 'ACTIVE',
          submitter: {
            connect: { id: testUser.id }
          },
          entityDetailsHardware: {
            create: {
              gpu: 'Apple M2 Ultra (76-core GPU)',
              processor: 'Apple M2 Ultra',
              memory: '128GB Unified Memory',
              storage: '1TB SSD',
              powerConsumption: '215W',
              price: '$4,999',
              availability: 'Available',
              useCases: ['ML Development', 'AI Research', 'Video Processing']
            }
          }
        }
      });
      
      console.log(`   ✅ Created ${hardware1.name}`);
      console.log(`   ✅ Created ${hardware2.name}`);
    }
    
    // Create Event entities
    if (entityTypeMap.event) {
      console.log('\n📅 Creating Event entities...');
      
      const event1 = await prisma.entity.create({
        data: {
          name: 'AI Conference 2024',
          slug: 'ai-conference-2024',
          websiteUrl: 'https://example.com/event-1',
          shortDescription: 'Premier AI conference bringing together industry leaders',
          description: 'Join us for the biggest AI conference of the year with top speakers and workshops.',
          entityType: {
            connect: { id: entityTypeMap.event }
          },
          status: 'ACTIVE',
          submitter: {
            connect: { id: testUser.id }
          },
          entityDetailsEvent: {
            create: {
              eventType: 'Conference',
              startDate: new Date('2024-09-15'),
              endDate: new Date('2024-09-17'),
              location: 'San Francisco, CA',
              price: '$599',
              registrationUrl: 'https://example.com/register-1',
              isOnline: false,
              keySpeakers: ['Dr. Andrew Ng', 'Fei-Fei Li', 'Yann LeCun'],
              targetAudience: ['AI Researchers', 'Data Scientists', 'ML Engineers'],
              topics: ['Deep Learning', 'Computer Vision', 'NLP', 'Robotics']
            }
          }
        }
      });
      
      const event2 = await prisma.entity.create({
        data: {
          name: 'Virtual ML Workshop Series',
          slug: 'virtual-ml-workshop-series',
          websiteUrl: 'https://example.com/event-2',
          shortDescription: 'Online workshop series for ML practitioners',
          description: 'Monthly virtual workshops covering practical ML topics and hands-on coding.',
          entityType: {
            connect: { id: entityTypeMap.event }
          },
          status: 'ACTIVE',
          submitter: {
            connect: { id: testUser.id }
          },
          entityDetailsEvent: {
            create: {
              eventType: 'Workshop',
              startDate: new Date('2024-07-01'),
              endDate: new Date('2024-12-31'),
              location: 'Online',
              price: 'Free',
              registrationUrl: 'https://example.com/register-2',
              isOnline: true,
              keySpeakers: ['Sarah Chen', 'Mike Rodriguez', 'Lisa Wang'],
              targetAudience: ['Beginners', 'Intermediate Practitioners'],
              topics: ['Python for ML', 'Model Deployment', 'MLOps', 'Feature Engineering']
            }
          }
        }
      });
      
      console.log(`   ✅ Created ${event1.name}`);
      console.log(`   ✅ Created ${event2.name}`);
    }
    
    console.log('\n🎉 Test data creation completed successfully!');
    
    // Verify creation
    const counts = await Promise.all([
      prisma.entity.count({ where: { entityType: { slug: 'course' } } }),
      prisma.entity.count({ where: { entityType: { slug: 'job' } } }),
      prisma.entity.count({ where: { entityType: { slug: 'hardware' } } }),
      prisma.entity.count({ where: { entityType: { slug: 'event' } } })
    ]);
    
    console.log('\n📊 Entity counts after creation:');
    console.log(`   • Courses: ${counts[0]}`);
    console.log(`   • Jobs: ${counts[1]}`);
    console.log(`   • Hardware: ${counts[2]}`);
    console.log(`   • Events: ${counts[3]}`);
    
  } catch (error) {
    console.error('❌ Test data creation failed:', error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the creation
createTestEntities()
  .then(() => {
    console.log('\n✨ Test data creation completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test data creation failed:', error);
    process.exit(1);
  });
