#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Parse SQL dump to extract table definitions
function parseSQLDump(sqlContent) {
  const tables = {};
  const lines = sqlContent.split('\n');
  
  for (const line of lines) {
    const trimmed = line.trim();
    
    // Start of table definition
    if (trimmed.startsWith('CREATE TABLE public.')) {
      const tableName = trimmed.match(/CREATE TABLE public\.(\w+)/)?.[1];
      if (tableName) {
        tables[tableName] = true;
      }
    }
  }
  
  return tables;
}

// Debug the parsing
function debugParsing() {
  console.log('🔍 Debugging schema parsing...\n');
  
  const sqlDumpPath = path.join(__dirname, '../docs/SQL-dump.md');
  const sqlContent = fs.readFileSync(sqlDumpPath, 'utf8');
  
  const sqlTables = parseSQLDump(sqlContent);
  const tableNames = Object.keys(sqlTables).sort();
  
  console.log(`📊 Found ${tableNames.length} tables in SQL dump:`);
  tableNames.forEach((table, index) => {
    console.log(`   ${index + 1}. ${table}`);
  });
  
  // Check specific tables
  const checkTables = ['app_settings', 'user_preferences', 'tool_requests', 'user_submitted_tools', 'profile_activities'];
  
  console.log('\n🔍 Checking specific tables:');
  checkTables.forEach(table => {
    const exists = tableNames.includes(table);
    console.log(`   ${exists ? '✅' : '❌'} ${table}`);
  });
}

debugParsing();
