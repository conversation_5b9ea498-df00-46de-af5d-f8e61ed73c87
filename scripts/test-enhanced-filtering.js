#!/usr/bin/env node

/**
 * Test Enhanced Filtering System
 * Tests the new entity type specific filtering capabilities
 */

const { PrismaClient } = require('../generated/prisma');

const prisma = new PrismaClient();

// Test data for different entity types
const testFilters = {
  course: {
    skill_levels: ['BEGINNER', 'INTERMEDIATE'],
    certificate_available: true,
    instructor_name: 'Dr.',
    enrollment_min: 100,
    enrollment_max: 10000
  },
  
  job: {
    employment_types: ['Full-time', 'Remote'],
    experience_levels: ['Mid', 'Senior'],
    location_types: ['Remote', 'Hybrid'],
    company_name: 'Tech',
    salary_min: 80,
    salary_max: 150
  },
  
  hardware: {
    hardware_types: ['GPU', 'CPU'],
    manufacturers: ['NVIDIA', 'Intel'],
    release_date_from: '2023-01-01',
    release_date_to: '2024-12-31',
    price_range: '$500'
  },
  
  event: {
    event_types: ['Conference', 'Workshop'],
    start_date_from: '2024-01-01',
    start_date_to: '2024-12-31',
    is_online: true,
    location: 'San Francisco'
  }
};

async function testBasicFiltering() {
  console.log('🧪 Testing basic filtering functionality...\n');
  
  try {
    // Test 1: Get all entities (baseline)
    const allEntities = await prisma.entity.findMany({
      take: 5,
      include: {
        entityType: true
      }
    });
    
    console.log(`📊 Found ${allEntities.length} entities for baseline test`);
    allEntities.forEach(entity => {
      console.log(`   • ${entity.name} (${entity.entityType.name})`);
    });
    
    return allEntities.length > 0;
    
  } catch (error) {
    console.error('❌ Basic filtering test failed:', error.message);
    return false;
  }
}

async function testEntityTypeFiltering() {
  console.log('\n🧪 Testing entity type specific filtering...\n');
  
  const results = {};
  
  try {
    // Test Course filtering
    console.log('📋 Testing Course filters...');
    const courseEntities = await prisma.entity.findMany({
      where: {
        entityType: {
          slug: 'course'
        }
      },
      include: {
        entityType: true,
        entityDetailsCourse: true
      },
      take: 3
    });
    
    results.courses = courseEntities.length;
    console.log(`   Found ${courseEntities.length} course entities`);
    
    // Test Job filtering
    console.log('📋 Testing Job filters...');
    const jobEntities = await prisma.entity.findMany({
      where: {
        entityType: {
          slug: 'job'
        }
      },
      include: {
        entityType: true,
        entityDetailsJob: true
      },
      take: 3
    });
    
    results.jobs = jobEntities.length;
    console.log(`   Found ${jobEntities.length} job entities`);
    
    // Test Hardware filtering
    console.log('📋 Testing Hardware filters...');
    const hardwareEntities = await prisma.entity.findMany({
      where: {
        entityType: {
          slug: 'hardware'
        }
      },
      include: {
        entityType: true,
        entityDetailsHardware: true
      },
      take: 3
    });
    
    results.hardware = hardwareEntities.length;
    console.log(`   Found ${hardwareEntities.length} hardware entities`);
    
    // Test Event filtering
    console.log('📋 Testing Event filters...');
    const eventEntities = await prisma.entity.findMany({
      where: {
        entityType: {
          slug: 'event'
        }
      },
      include: {
        entityType: true,
        entityDetailsEvent: true
      },
      take: 3
    });
    
    results.events = eventEntities.length;
    console.log(`   Found ${eventEntities.length} event entities`);
    
    return results;
    
  } catch (error) {
    console.error('❌ Entity type filtering test failed:', error.message);
    return null;
  }
}

async function testDetailLevelFiltering() {
  console.log('\n🧪 Testing detail-level filtering...\n');
  
  try {
    // Test Course detail filtering
    console.log('📋 Testing Course detail filters...');
    const coursesWithCertificates = await prisma.entity.findMany({
      where: {
        AND: [
          {
            entityType: {
              slug: 'course'
            }
          },
          {
            entityDetailsCourse: {
              certificateAvailable: true
            }
          }
        ]
      },
      include: {
        entityType: true,
        entityDetailsCourse: true
      },
      take: 2
    });
    
    console.log(`   Found ${coursesWithCertificates.length} courses with certificates`);
    
    // Test Job detail filtering
    console.log('📋 Testing Job detail filters...');
    const remoteJobs = await prisma.entity.findMany({
      where: {
        AND: [
          {
            entityType: {
              slug: 'job'
            }
          },
          {
            entityDetailsJob: {
              isRemote: true
            }
          }
        ]
      },
      include: {
        entityType: true,
        entityDetailsJob: true
      },
      take: 2
    });
    
    console.log(`   Found ${remoteJobs.length} remote jobs`);
    
    return {
      coursesWithCertificates: coursesWithCertificates.length,
      remoteJobs: remoteJobs.length
    };
    
  } catch (error) {
    console.error('❌ Detail-level filtering test failed:', error.message);
    return null;
  }
}

async function testComplexFiltering() {
  console.log('\n🧪 Testing complex filtering combinations...\n');
  
  try {
    // Test complex AND conditions
    console.log('📋 Testing complex AND conditions...');
    const complexQuery = await prisma.entity.findMany({
      where: {
        AND: [
          {
            status: 'ACTIVE'
          },
          {
            OR: [
              {
                entityType: {
                  slug: 'course'
                }
              },
              {
                entityType: {
                  slug: 'job'
                }
              }
            ]
          }
        ]
      },
      include: {
        entityType: true
      },
      take: 5
    });
    
    console.log(`   Found ${complexQuery.length} entities with complex filters`);
    
    return {
      complexResults: complexQuery.length
    };
    
  } catch (error) {
    console.error('❌ Complex filtering test failed:', error.message);
    return null;
  }
}

async function showFilteringCapabilities() {
  console.log('\n📊 ENHANCED FILTERING CAPABILITIES SUMMARY');
  console.log('=' .repeat(60));
  
  console.log('\n✅ IMPLEMENTED FILTERS:');
  console.log('   🎓 Course Filters:');
  console.log('      • skill_levels (BEGINNER, INTERMEDIATE, ADVANCED)');
  console.log('      • certificate_available (boolean)');
  console.log('      • instructor_name (text search)');
  console.log('      • enrollment_min/max (number ranges)');
  console.log('      • duration_text (text search)');
  console.log('      • prerequisites (text search)');
  console.log('      • has_syllabus (boolean)');
  
  console.log('\n   💼 Job Filters:');
  console.log('      • employment_types (Full-time, Part-time, Contract)');
  console.log('      • experience_levels (Entry, Mid, Senior)');
  console.log('      • location_types (Remote, On-site, Hybrid)');
  console.log('      • company_name (text search)');
  console.log('      • job_title (text search)');
  console.log('      • salary_min/max (number ranges)');
  console.log('      • job_description (text search)');
  console.log('      • has_application_url (boolean)');
  
  console.log('\n   🖥️  Hardware Filters:');
  console.log('      • hardware_types (GPU, CPU, FPGA, TPU)');
  console.log('      • manufacturers (NVIDIA, Intel, AMD)');
  console.log('      • release_date_from/to (date ranges)');
  console.log('      • price_range (text search)');
  console.log('      • specifications_search (JSON search)');
  console.log('      • has_datasheet (boolean)');
  
  console.log('\n   📅 Event Filters:');
  console.log('      • event_types (Conference, Workshop, Webinar)');
  console.log('      • start_date_from/to (date ranges)');
  console.log('      • end_date_from/to (date ranges)');
  console.log('      • is_online (boolean)');
  console.log('      • location (text search)');
  console.log('      • has_registration_url (boolean)');
  
  console.log('\n🚀 USAGE EXAMPLES:');
  console.log('   GET /entities?entity_type_filters[course][skill_levels]=BEGINNER,INTERMEDIATE');
  console.log('   GET /entities?entity_type_filters[job][employment_types]=Full-time&entity_type_filters[job][location_types]=Remote');
  console.log('   GET /entities?entity_type_filters[hardware][manufacturers]=NVIDIA&entity_type_filters[hardware][price_range]=$500');
  console.log('   GET /entities?entity_type_filters[event][is_online]=true&entity_type_filters[event][start_date_from]=2024-01-01');
}

async function runAllTests() {
  console.log('🚀 ENHANCED FILTERING SYSTEM TESTS');
  console.log('=' .repeat(50));
  
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful\n');
    
    // Run tests
    const basicTest = await testBasicFiltering();
    const entityTypeTest = await testEntityTypeFiltering();
    const detailTest = await testDetailLevelFiltering();
    const complexTest = await testComplexFiltering();
    
    // Show capabilities
    await showFilteringCapabilities();
    
    // Summary
    console.log('\n🎯 TEST RESULTS SUMMARY');
    console.log('=' .repeat(50));
    console.log(`✅ Basic filtering: ${basicTest ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Entity type filtering: ${entityTypeTest ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Detail-level filtering: ${detailTest ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Complex filtering: ${complexTest ? 'PASSED' : 'FAILED'}`);
    
    if (entityTypeTest) {
      console.log('\n📊 Entity Type Coverage:');
      console.log(`   • Courses: ${entityTypeTest.courses} entities`);
      console.log(`   • Jobs: ${entityTypeTest.jobs} entities`);
      console.log(`   • Hardware: ${entityTypeTest.hardware} entities`);
      console.log(`   • Events: ${entityTypeTest.events} entities`);
    }
    
    console.log('\n🎉 Enhanced filtering system is ready for use!');
    
  } catch (error) {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the tests
runAllTests()
  .then(() => {
    console.log('\n✨ All tests completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  });
