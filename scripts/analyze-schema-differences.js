#!/usr/bin/env node

/**
 * Schema Comparison Tool
 * Compares the current Prisma schema with the SQL dump to identify differences
 */

const fs = require('fs');
const path = require('path');

// Parse SQL dump to extract table definitions
function parseSQLDump(sqlContent) {
  const tables = {};
  const lines = sqlContent.split('\n');
  
  let currentTable = null;
  let inTableDefinition = false;
  
  for (const line of lines) {
    const trimmed = line.trim();
    
    // Start of table definition
    if (trimmed.startsWith('CREATE TABLE public.')) {
      const tableName = trimmed.match(/CREATE TABLE public\.(\w+)/)?.[1];
      if (tableName) {
        currentTable = tableName;
        tables[tableName] = {
          columns: {},
          constraints: [],
          indexes: []
        };
        inTableDefinition = true;
      }
    }
    
    // End of table definition
    else if (trimmed === ');' && inTableDefinition) {
      inTableDefinition = false;
      currentTable = null;
    }
    
    // Column or constraint definition
    else if (inTableDefinition && currentTable && trimmed) {
      if (trimmed.startsWith('CONSTRAINT')) {
        tables[currentTable].constraints.push(trimmed);
      } else if (!trimmed.startsWith('--')) {
        // Parse column definition
        const columnMatch = trimmed.match(/^(\w+)\s+(.+?)(?:,\s*)?$/);
        if (columnMatch) {
          const [, columnName, columnDef] = columnMatch;
          tables[currentTable].columns[columnName] = columnDef.replace(/,$/, '');
        }
      }
    }
  }
  
  return tables;
}

// Parse Prisma schema to extract model definitions
function parsePrismaSchema(schemaContent) {
  const models = {};
  const lines = schemaContent.split('\n');
  
  let currentModel = null;
  let inModelDefinition = false;
  
  for (const line of lines) {
    const trimmed = line.trim();
    
    // Start of model definition
    if (trimmed.startsWith('model ')) {
      const modelName = trimmed.match(/model (\w+)/)?.[1];
      if (modelName) {
        currentModel = modelName;
        models[modelName] = {
          fields: {},
          relations: [],
          attributes: []
        };
        inModelDefinition = true;
      }
    }
    
    // End of model definition
    else if (trimmed === '}' && inModelDefinition) {
      inModelDefinition = false;
      currentModel = null;
    }
    
    // Field definition
    else if (inModelDefinition && currentModel && trimmed && !trimmed.startsWith('//')) {
      if (trimmed.startsWith('@@')) {
        models[currentModel].attributes.push(trimmed);
      } else {
        const fieldMatch = trimmed.match(/^(\w+)\s+(.+)$/);
        if (fieldMatch) {
          const [, fieldName, fieldDef] = fieldMatch;
          models[currentModel].fields[fieldName] = fieldDef;
        }
      }
    }
  }
  
  return models;
}

// Convert Prisma model names to SQL table names
function modelToTableName(modelName) {
  // Manual mapping for known models to their actual table names
  const manualMappings = {
    'User': 'users',
    'EntityType': 'entity_types',
    'Entity': 'entities',
    'Category': 'categories',
    'Tag': 'tags',
    'EntityTag': 'entity_tags',
    'EntityCategory': 'entity_categories',
    'Review': 'reviews',
    'ReviewVote': 'review_votes',
    'UserSavedEntity': 'user_saved_entities',
    'UserFollowedTag': 'user_followed_tags',
    'UserFollowedCategory': 'user_followed_categories',
    'UserActivityLog': 'user_activity_logs',
    'BadgeType': 'badge_types',
    'UserBadge': 'user_badges',
    'EntityBadge': 'entity_badges',
    'EntityFeature': 'entity_features',
    'AppSetting': 'app_settings',
    'Feature': 'features',
    'UserPreferences': 'user_preferences',
    'ToolRequest': 'tool_requests',
    'UserSubmittedTool': 'user_submitted_tools',
    'ProfileActivity': 'profile_activities'
  };

  if (manualMappings[modelName]) {
    return manualMappings[modelName];
  }

  // Convert PascalCase to snake_case for entity details
  return modelName
    .replace(/([A-Z])/g, '_$1')
    .toLowerCase()
    .replace(/^_/, '');
}

// Main comparison function
function compareSchemas() {
  console.log('🔍 Analyzing database schema differences...\n');
  
  try {
    // Read files
    const sqlDumpPath = path.join(__dirname, '../docs/SQL-dump.md');
    const prismaSchemaPath = path.join(__dirname, '../prisma/schema.prisma');
    
    const sqlContent = fs.readFileSync(sqlDumpPath, 'utf8');
    const prismaContent = fs.readFileSync(prismaSchemaPath, 'utf8');
    
    // Parse schemas
    const sqlTables = parseSQLDump(sqlContent);
    const prismaModels = parsePrismaSchema(prismaContent);
    
    console.log(`📊 SQL Dump: ${Object.keys(sqlTables).length} tables`);
    console.log(`📊 Prisma Schema: ${Object.keys(prismaModels).length} models\n`);
    
    // Find missing tables
    const missingTables = [];
    const extraTables = [];
    const prismaTableNames = Object.keys(prismaModels).map(modelToTableName);
    
    // Check for tables in SQL dump but not in Prisma
    for (const tableName of Object.keys(sqlTables)) {
      if (!prismaTableNames.includes(tableName)) {
        missingTables.push(tableName);
      }
    }
    
    // Check for models in Prisma but not in SQL dump
    for (const modelName of Object.keys(prismaModels)) {
      const tableName = modelToTableName(modelName);
      if (!Object.keys(sqlTables).includes(tableName)) {
        extraTables.push({ model: modelName, table: tableName });
      }
    }
    
    // Report findings
    console.log('🔍 SCHEMA COMPARISON RESULTS');
    console.log('=' .repeat(50));
    
    if (missingTables.length > 0) {
      console.log(`\n❌ MISSING TABLES (${missingTables.length}):`);
      console.log('   Tables in SQL dump but not in Prisma schema:');
      missingTables.forEach(table => {
        console.log(`   • ${table}`);
      });
    }
    
    if (extraTables.length > 0) {
      console.log(`\n⚠️  EXTRA MODELS (${extraTables.length}):`);
      console.log('   Models in Prisma but not in SQL dump:');
      extraTables.forEach(({ model, table }) => {
        console.log(`   • ${model} → ${table}`);
      });
    }
    
    if (missingTables.length === 0 && extraTables.length === 0) {
      console.log('\n✅ ALL TABLES MATCH');
      console.log('   No missing tables found between schemas');
    }
    
    // Detailed analysis of matching tables
    console.log('\n📋 DETAILED ANALYSIS:');
    const matchingTables = Object.keys(sqlTables).filter(table => 
      prismaTableNames.includes(table)
    );
    
    console.log(`   • Matching tables: ${matchingTables.length}`);
    console.log(`   • Missing tables: ${missingTables.length}`);
    console.log(`   • Extra models: ${extraTables.length}`);
    
    return {
      missingTables,
      extraTables,
      matchingTables,
      sqlTables,
      prismaModels
    };
    
  } catch (error) {
    console.error('❌ Error analyzing schemas:', error.message);
    throw error;
  }
}

// Run the analysis
if (require.main === module) {
  try {
    compareSchemas();
    console.log('\n✨ Schema analysis completed');
    process.exit(0);
  } catch (error) {
    console.error('💥 Schema analysis failed:', error);
    process.exit(1);
  }
}

module.exports = { compareSchemas };
