#!/usr/bin/env node

/**
 * Comprehensive test script for enhanced filtering capabilities
 * Tests all new filters, entity type specific filters, and sorting options
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const API_ENDPOINT = `${BASE_URL}/entities`;

// Test configuration
const TESTS = {
  // Core entity filters
  coreFilters: [
    {
      name: 'Rating Filter - Minimum 4.0',
      params: { rating_min: 4.0, limit: 5 }
    },
    {
      name: 'Rating Range Filter - 3.0 to 4.5',
      params: { rating_min: 3.0, rating_max: 4.5, limit: 5 }
    },
    {
      name: 'Review Count Filter - Minimum 10 reviews',
      params: { review_count_min: 10, limit: 5 }
    },
    {
      name: 'Affiliate Status Filter - Approved only',
      params: { affiliate_status: 'APPROVED', limit: 5 }
    },
    {
      name: 'Has Affiliate Link Filter',
      params: { has_affiliate_link: true, limit: 5 }
    }
  ],

  // Advanced sorting tests
  sortingTests: [
    {
      name: 'Sort by Average Rating (Desc)',
      params: { sortBy: 'averageRating', sortOrder: 'desc', limit: 5 }
    },
    {
      name: 'Sort by Review Count (Desc)',
      params: { sortBy: 'reviewCount', sortOrder: 'desc', limit: 5 }
    },
    {
      name: 'Sort by Save Count (Desc)',
      params: { sortBy: 'saveCount', sortOrder: 'desc', limit: 5 }
    },
    {
      name: 'Sort by Popularity (Desc)',
      params: { sortBy: 'popularity', sortOrder: 'desc', limit: 5 }
    },
    {
      name: 'Sort by Name (Asc)',
      params: { sortBy: 'name', sortOrder: 'asc', limit: 5 }
    },
    {
      name: 'Sort by Founded Year (Desc)',
      params: { sortBy: 'foundedYear', sortOrder: 'desc', limit: 5 }
    }
  ],

  // Entity type specific filters
  entityTypeFilters: [
    {
      name: 'Tool Filters - Free Tier with API Access',
      params: {
        entity_type_filters: {
          tool: {
            has_free_tier: true,
            has_api: true,
            technical_levels: ['BEGINNER', 'INTERMEDIATE']
          }
        },
        limit: 5
      }
    },
    {
      name: 'Course Filters - Beginner Level with Certificates',
      params: {
        entity_type_filters: {
          course: {
            skill_levels: ['BEGINNER'],
            certificate_available: true,
            enrollment_min: 100
          }
        },
        limit: 5
      }
    },
    {
      name: 'Job Filters - Remote Full-time with Salary Range',
      params: {
        entity_type_filters: {
          job: {
            employment_types: ['Full-time'],
            location_types: ['Remote'],
            salary_min: 80,
            salary_max: 150
          }
        },
        limit: 5
      }
    },
    {
      name: 'Agency Filters - AI Strategy Services',
      params: {
        entity_type_filters: {
          agency: {
            services_offered: ['AI Strategy', 'Machine Learning'],
            industry_focus: ['Healthcare', 'Finance']
          }
        },
        limit: 5
      }
    },
    {
      name: 'Software Filters - Open Source Python Tools',
      params: {
        entity_type_filters: {
          software: {
            open_source: true,
            programming_languages: ['Python'],
            platform_compatibility: ['Linux', 'macOS']
          }
        },
        limit: 5
      }
    },
    {
      name: 'Research Paper Filters - Recent ML Papers',
      params: {
        entity_type_filters: {
          research_paper: {
            research_areas: ['Machine Learning', 'Deep Learning'],
            publication_date_from: '2023-01-01',
            citation_count_min: 50
          }
        },
        limit: 5
      }
    },
    {
      name: 'Podcast Filters - AI Topics on Spotify',
      params: {
        entity_type_filters: {
          podcast: {
            main_topics: ['AI', 'Machine Learning'],
            has_spotify: true,
            frequency: ['Weekly']
          }
        },
        limit: 5
      }
    },
    {
      name: 'Book Filters - Recent AI Books',
      params: {
        entity_type_filters: {
          book: {
            publication_date_from: '2020-01-01',
            formats: ['eBook', 'Paperback'],
            page_count_min: 200
          }
        },
        limit: 5
      }
    }
  ],

  // Combined filters tests
  combinedFilters: [
    {
      name: 'Combined - High-rated Free AI Tools',
      params: {
        rating_min: 4.0,
        hasFreeTier: true,
        entity_type_filters: {
          tool: {
            has_free_tier: true,
            technical_levels: ['BEGINNER', 'INTERMEDIATE']
          }
        },
        sortBy: 'averageRating',
        sortOrder: 'desc',
        limit: 5
      }
    },
    {
      name: 'Combined - Popular Remote AI Jobs',
      params: {
        review_count_min: 5,
        entity_type_filters: {
          job: {
            location_types: ['Remote'],
            salary_min: 100
          }
        },
        sortBy: 'popularity',
        sortOrder: 'desc',
        limit: 5
      }
    }
  ]
};

/**
 * Execute a single test
 */
async function executeTest(testName, params) {
  try {
    console.log(`\n🧪 Testing: ${testName}`);
    console.log(`📋 Parameters:`, JSON.stringify(params, null, 2));
    
    const response = await axios.get(API_ENDPOINT, { params });
    
    console.log(`✅ Status: ${response.status}`);
    console.log(`📊 Results: ${response.data.total} total, ${response.data.data.length} returned`);
    
    if (response.data.data.length > 0) {
      console.log(`📝 Sample entity: ${response.data.data[0].name}`);
      
      // Show relevant fields based on test type
      if (params.sortBy === 'averageRating') {
        console.log(`⭐ Average rating: ${response.data.data[0].averageRating || 'N/A'}`);
      }
      if (params.sortBy === 'reviewCount') {
        console.log(`💬 Review count: ${response.data.data[0].reviewCount || 'N/A'}`);
      }
      if (params.entity_type_filters) {
        console.log(`🏷️  Entity type: ${response.data.data[0].entityType?.name || 'N/A'}`);
      }
    }
    
    return { success: true, count: response.data.total };
  } catch (error) {
    console.log(`❌ Error: ${error.response?.status || 'Network'} - ${error.response?.data?.message || error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Enhanced Filtering Tests');
  console.log(`🌐 API Endpoint: ${API_ENDPOINT}`);
  
  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    details: []
  };

  // Test core filters
  console.log('\n' + '='.repeat(60));
  console.log('📊 CORE ENTITY FILTERS');
  console.log('='.repeat(60));
  
  for (const test of TESTS.coreFilters) {
    const result = await executeTest(test.name, test.params);
    results.total++;
    if (result.success) results.passed++;
    else results.failed++;
    results.details.push({ test: test.name, ...result });
  }

  // Test sorting
  console.log('\n' + '='.repeat(60));
  console.log('🔄 ADVANCED SORTING');
  console.log('='.repeat(60));
  
  for (const test of TESTS.sortingTests) {
    const result = await executeTest(test.name, test.params);
    results.total++;
    if (result.success) results.passed++;
    else results.failed++;
    results.details.push({ test: test.name, ...result });
  }

  // Test entity type filters
  console.log('\n' + '='.repeat(60));
  console.log('🎯 ENTITY TYPE SPECIFIC FILTERS');
  console.log('='.repeat(60));
  
  for (const test of TESTS.entityTypeFilters) {
    const result = await executeTest(test.name, test.params);
    results.total++;
    if (result.success) results.passed++;
    else results.failed++;
    results.details.push({ test: test.name, ...result });
  }

  // Test combined filters
  console.log('\n' + '='.repeat(60));
  console.log('🔗 COMBINED FILTERS');
  console.log('='.repeat(60));
  
  for (const test of TESTS.combinedFilters) {
    const result = await executeTest(test.name, test.params);
    results.total++;
    if (result.success) results.passed++;
    else results.failed++;
    results.details.push({ test: test.name, ...result });
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📈 TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${results.total}`);
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📊 Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);

  if (results.failed > 0) {
    console.log('\n❌ Failed Tests:');
    results.details
      .filter(r => !r.success)
      .forEach(r => console.log(`  - ${r.test}: ${r.error}`));
  }

  console.log('\n🎉 Enhanced filtering test completed!');
  
  // Exit with appropriate code
  process.exit(results.failed > 0 ? 1 : 0);
}

// Run tests
runAllTests().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
