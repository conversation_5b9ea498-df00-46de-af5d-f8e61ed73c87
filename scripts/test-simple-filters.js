#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testFilters() {
  console.log('🧪 Testing Enhanced Filters\n');

  // Test 1: Basic core filters
  console.log('1. Testing core filters...');
  try {
    const response1 = await axios.get(`${BASE_URL}/entities`, {
      params: {
        affiliate_status: 'APPROVED',
        limit: 2
      }
    });
    console.log(`✅ Affiliate status filter: ${response1.data.total} results`);
  } catch (error) {
    console.log(`❌ Affiliate status filter failed: ${error.response?.data?.message || error.message}`);
  }

  // Test 2: Advanced sorting
  console.log('\n2. Testing advanced sorting...');
  try {
    const response2 = await axios.get(`${BASE_URL}/entities`, {
      params: {
        sortBy: 'name',
        sortOrder: 'asc',
        limit: 2
      }
    });
    console.log(`✅ Name sorting: ${response2.data.data[0]?.name || 'No results'}`);
  } catch (error) {
    console.log(`❌ Name sorting failed: ${error.response?.data?.message || error.message}`);
  }

  // Test 3: Rating filters
  console.log('\n3. Testing rating filters...');
  try {
    const response3 = await axios.get(`${BASE_URL}/entities`, {
      params: {
        rating_min: 1,
        limit: 2
      }
    });
    console.log(`✅ Rating filter: ${response3.data.total} results`);
  } catch (error) {
    console.log(`❌ Rating filter failed: ${error.response?.data?.message || error.message}`);
  }

  // Test 4: Entity type filters (simple)
  console.log('\n4. Testing entity type filters...');
  try {
    const response4 = await axios.get(`${BASE_URL}/entities`, {
      params: {
        'entity_type_filters[course][certificate_available]': true,
        limit: 2
      }
    });
    console.log(`✅ Course certificate filter: ${response4.data.total} results`);
  } catch (error) {
    console.log(`❌ Course certificate filter failed: ${error.response?.data?.message || error.message}`);
  }

  // Test 5: Entity type filters (JSON body)
  console.log('\n5. Testing entity type filters with JSON...');
  try {
    const response5 = await axios.get(`${BASE_URL}/entities`, {
      params: {
        entity_type_filters: JSON.stringify({
          course: {
            certificate_available: true
          }
        }),
        limit: 2
      }
    });
    console.log(`✅ Course JSON filter: ${response5.data.total} results`);
  } catch (error) {
    console.log(`❌ Course JSON filter failed: ${error.response?.data?.message || error.message}`);
  }

  console.log('\n🎉 Filter testing completed!');
}

testFilters().catch(console.error);
