-- Upvote Performance Monitoring Queries
-- Run these queries to verify the upvote functionality and monitor performance

-- 1. Verify the user_upvotes table exists and has correct structure
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'user_upvotes'
ORDER BY ordinal_position;

-- 2. Verify the upvote_count column exists in entities table
SELECT 
    column_name, 
    data_type, 
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'entities' 
  AND column_name = 'upvote_count';

-- 3. Check if the trigger function exists
SELECT 
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name = 'update_entity_upvote_count';

-- 4. Check if the trigger exists
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
  AND trigger_name = 'trigger_update_entity_upvote_count';

-- 5. Performance monitoring: Check upvote count accuracy
-- This query compares the denormalized upvote_count with actual count
SELECT 
    e.id,
    e.name,
    e.upvote_count as stored_count,
    COUNT(uv.entity_id) as actual_count,
    (e.upvote_count - COUNT(uv.entity_id)) as difference
FROM entities e
LEFT JOIN user_upvotes uv ON e.id = uv.entity_id
GROUP BY e.id, e.name, e.upvote_count
HAVING e.upvote_count != COUNT(uv.entity_id)
ORDER BY difference DESC;

-- 6. Top upvoted entities
SELECT 
    e.id,
    e.name,
    e.upvote_count,
    e.entity_type_id
FROM entities e
WHERE e.upvote_count > 0
ORDER BY e.upvote_count DESC
LIMIT 20;

-- 7. User upvote activity summary
SELECT 
    u.id,
    u.username,
    u.display_name,
    COUNT(uv.entity_id) as total_upvotes,
    MIN(uv.created_at) as first_upvote,
    MAX(uv.created_at) as latest_upvote
FROM users u
JOIN user_upvotes uv ON u.id = uv.user_id
GROUP BY u.id, u.username, u.display_name
ORDER BY total_upvotes DESC
LIMIT 20;

-- 8. Upvote trends over time (daily)
SELECT 
    DATE(uv.created_at) as upvote_date,
    COUNT(*) as upvotes_count,
    COUNT(DISTINCT uv.user_id) as unique_users,
    COUNT(DISTINCT uv.entity_id) as unique_entities
FROM user_upvotes uv
WHERE uv.created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(uv.created_at)
ORDER BY upvote_date DESC;

-- 9. Performance test: Trigger execution time
-- This query helps monitor if the trigger is performing well
EXPLAIN ANALYZE
INSERT INTO user_upvotes (user_id, entity_id) 
VALUES (
    (SELECT id FROM users LIMIT 1),
    (SELECT id FROM entities LIMIT 1)
) 
ON CONFLICT (user_id, entity_id) DO NOTHING;

-- 10. Index performance check
-- Verify that queries on user_upvotes are using indexes efficiently
EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM user_upvotes 
WHERE user_id = (SELECT id FROM users LIMIT 1);

EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM user_upvotes 
WHERE entity_id = (SELECT id FROM entities LIMIT 1);

-- 11. Check for any orphaned upvotes (should be none due to foreign keys)
SELECT 
    'Orphaned user upvotes' as issue_type,
    COUNT(*) as count
FROM user_upvotes uv
LEFT JOIN users u ON uv.user_id = u.id
WHERE u.id IS NULL

UNION ALL

SELECT 
    'Orphaned entity upvotes' as issue_type,
    COUNT(*) as count
FROM user_upvotes uv
LEFT JOIN entities e ON uv.entity_id = e.id
WHERE e.id IS NULL;

-- 12. Database size monitoring
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public' 
  AND tablename = 'user_upvotes';

-- 13. Trigger performance monitoring
-- Check if there are any slow queries related to upvote operations
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%user_upvotes%' 
   OR query LIKE '%upvote_count%'
ORDER BY mean_time DESC
LIMIT 10;
