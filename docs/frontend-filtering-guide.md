# 🎯 Frontend Guide: Enhanced Entity Filtering API

## Overview

The GET `/entities` endpoint now provides **world-class filtering capabilities** for AI entity discovery. This guide shows frontend developers how to leverage all the new filtering options.

## 🚀 Quick Start

### Basic Usage
```javascript
// Simple entity listing
const response = await fetch('/entities?limit=10&sortBy=name&sortOrder=asc');
const { data, total, pagination } = await response.json();
```

### Advanced Filtering
```javascript
// High-rated AI tools with free tiers
const params = new URLSearchParams({
  rating_min: '4.0',
  sortBy: 'averageRating',
  sortOrder: 'desc',
  limit: '20'
});

const response = await fetch(`/entities?${params}`);
```

## 📊 Core Entity Filters

### Rating & Review Filters
```javascript
const filters = {
  rating_min: 4.0,           // Minimum average rating (1-5)
  rating_max: 5.0,           // Maximum average rating (1-5)
  review_count_min: 10,      // Minimum number of reviews
  review_count_max: 1000,    // Maximum number of reviews
};
```

### Business & Affiliate Filters
```javascript
const businessFilters = {
  affiliate_status: 'APPROVED',     // NONE, APPLIED, APPROVED, REJECTED
  has_affiliate_link: true,         // Boolean: has affiliate/referral links
};
```

### Standard Filters (Existing)
```javascript
const standardFilters = {
  status: 'ACTIVE',                 // Entity status
  entityTypeIds: ['uuid1', 'uuid2'], // Filter by entity types
  categoryIds: ['uuid1', 'uuid2'],   // Filter by categories
  tagIds: ['uuid1', 'uuid2'],       // Filter by tags
  searchTerm: 'AI tool',            // Text search
  hasFreeTier: true,                // Boolean filters
  locationSearch: 'San Francisco',  // Location search
  createdAtFrom: '2023-01-01',      // Date range filters
  createdAtTo: '2024-12-31',
};
```

## 🔄 Advanced Sorting

### Available Sort Options
```javascript
const sortOptions = {
  // Standard sorting
  sortBy: 'createdAt',      // Default
  sortBy: 'updatedAt',      // Last updated
  sortBy: 'name',           // Alphabetical
  sortBy: 'foundedYear',    // Company founding year
  
  // Advanced sorting (NEW!)
  sortBy: 'averageRating',  // By user ratings
  sortBy: 'reviewCount',    // By review popularity
  sortBy: 'saveCount',      // By user saves
  sortBy: 'popularity',     // Combined popularity score
  sortBy: 'relevance',      // Search relevance
  
  sortOrder: 'desc'         // 'asc' or 'desc'
};
```

### Usage Examples
```javascript
// Most popular AI tools
const popularTools = await fetch('/entities?sortBy=popularity&sortOrder=desc&limit=10');

// Highest rated entities
const topRated = await fetch('/entities?sortBy=averageRating&sortOrder=desc&rating_min=4.0');

// Most reviewed entities
const mostReviewed = await fetch('/entities?sortBy=reviewCount&sortOrder=desc&review_count_min=5');
```

## 🎯 Entity Type Specific Filters

### Tool Filters
```javascript
const toolFilters = {
  entity_type_filters: {
    tool: {
      // Technical requirements
      technical_levels: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'],
      learning_curves: ['LOW', 'MEDIUM', 'HIGH'],
      
      // Pricing & access
      pricing_models: ['FREE', 'FREEMIUM', 'SUBSCRIPTION', 'PAY_PER_USE'],
      price_ranges: ['FREE', 'LOW', 'MEDIUM', 'HIGH'],
      has_api: true,
      has_free_tier: true,
      
      // Features & support
      open_source: true,
      mobile_support: true,
      demo_available: true,
      has_live_chat: true,
      
      // Technology
      platforms: ['Web', 'iOS', 'Android', 'Desktop'],
      integrations: ['Slack', 'Discord', 'Zapier'],
      frameworks: ['TensorFlow', 'PyTorch', 'Hugging Face'],
      libraries: ['OpenAI', 'Anthropic', 'Cohere'],
      deployment_options: ['Cloud', 'On-premise', 'Hybrid'],
      support_channels: ['Email', 'Chat', 'Phone', 'Community'],
      
      // Search within arrays
      key_features_search: 'natural language processing',
      use_cases_search: 'content generation',
      target_audience_search: 'developers',
      customization_level: 'high',
      pricing_details_search: 'per user'
    }
  }
};
```

### Course Filters
```javascript
const courseFilters = {
  entity_type_filters: {
    course: {
      skill_levels: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'],
      course_types: ['Online', 'In-person', 'Hybrid'],
      certificate_available: true,
      enrollment_min: 100,
      enrollment_max: 10000,
      duration_weeks_min: 4,
      duration_weeks_max: 12,
      price_min: 0,
      price_max: 500,
      languages: ['English', 'Spanish', 'French'],
      prerequisites_search: 'python',
      topics_search: 'machine learning'
    }
  }
};
```

### Job Filters
```javascript
const jobFilters = {
  entity_type_filters: {
    job: {
      employment_types: ['Full-time', 'Part-time', 'Contract', 'Freelance'],
      location_types: ['Remote', 'On-site', 'Hybrid'],
      experience_levels: ['Entry', 'Mid', 'Senior', 'Lead'],
      salary_min: 80,        // In thousands
      salary_max: 200,
      equity_offered: true,
      visa_sponsorship: true,
      required_skills: ['Python', 'Machine Learning', 'TensorFlow'],
      company_size_ranges: ['1-10', '11-50', '51-200', '201-500', '500+'],
      industries: ['Technology', 'Healthcare', 'Finance'],
      benefits_search: 'health insurance',
      requirements_search: 'PhD preferred'
    }
  }
};
```

### Agency Filters
```javascript
const agencyFilters = {
  entity_type_filters: {
    agency: {
      services_offered: ['AI Strategy', 'Machine Learning', 'Data Science'],
      industry_focus: ['Healthcare', 'Finance', 'E-commerce'],
      target_client_size: ['Startup', 'SMB', 'Enterprise'],
      target_audience: ['CTOs', 'Data Scientists', 'Business Leaders'],
      location_summary: 'San Francisco',
      has_portfolio: true,
      pricing_info_search: 'hourly rate',
      services_search: 'machine learning',
      industry_search: 'healthcare'
    }
  }
};
```

### Research Paper Filters
```javascript
const paperFilters = {
  entity_type_filters: {
    research_paper: {
      authors: ['Geoffrey Hinton', 'Yann LeCun'],
      research_areas: ['Machine Learning', 'Computer Vision'],
      publication_venues: ['NeurIPS', 'ICML', 'Nature'],
      keywords: ['transformer', 'attention', 'neural network'],
      doi: '10.1038',
      publication_date_from: '2020-01-01',
      publication_date_to: '2024-12-31',
      citation_count_min: 100,
      citation_count_max: 10000,
      abstract_search: 'attention mechanism',
      authors_search: 'hinton',
      research_areas_search: 'nlp'
    }
  }
};
```

## 🔗 Combining Filters

### Complex Filter Example
```javascript
const complexFilters = {
  // Core filters
  rating_min: 4.0,
  review_count_min: 10,
  affiliate_status: 'APPROVED',
  
  // Sorting
  sortBy: 'popularity',
  sortOrder: 'desc',
  
  // Entity type specific
  entity_type_filters: {
    tool: {
      has_free_tier: true,
      technical_levels: ['BEGINNER', 'INTERMEDIATE'],
      platforms: ['Web', 'Mobile'],
      integrations: ['Slack', 'Discord']
    }
  },
  
  // Pagination
  page: 1,
  limit: 20
};

const url = new URL('/entities', window.location.origin);
Object.entries(complexFilters).forEach(([key, value]) => {
  if (key === 'entity_type_filters') {
    url.searchParams.set(key, JSON.stringify(value));
  } else if (Array.isArray(value)) {
    value.forEach(v => url.searchParams.append(key, v));
  } else {
    url.searchParams.set(key, value);
  }
});

const response = await fetch(url);
```

## 📱 Frontend Implementation Patterns

### React Hook Example
```javascript
import { useState, useEffect } from 'react';

const useEntityFilters = () => {
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  
  const [entities, setEntities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  const fetchEntities = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (key === 'entity_type_filters' && value) {
          params.set(key, JSON.stringify(value));
        } else if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v));
        } else if (value !== undefined && value !== null) {
          params.set(key, value);
        }
      });

      const response = await fetch(`/entities?${params}`);
      const data = await response.json();
      
      setEntities(data.data);
      setTotal(data.total);
    } catch (error) {
      console.error('Failed to fetch entities:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEntities();
  }, [filters]);

  const updateFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const updateEntityTypeFilters = (entityType, typeFilters) => {
    setFilters(prev => ({
      ...prev,
      entity_type_filters: {
        ...prev.entity_type_filters,
        [entityType]: typeFilters
      },
      page: 1
    }));
  };

  return {
    entities,
    loading,
    total,
    filters,
    updateFilters,
    updateEntityTypeFilters
  };
};
```

### Filter Component Example
```javascript
const EntityFilters = ({ onFiltersChange }) => {
  const [rating, setRating] = useState({ min: '', max: '' });
  const [sortBy, setSortBy] = useState('createdAt');
  const [toolFilters, setToolFilters] = useState({});

  const handleApplyFilters = () => {
    const filters = {
      ...(rating.min && { rating_min: parseFloat(rating.min) }),
      ...(rating.max && { rating_max: parseFloat(rating.max) }),
      sortBy,
      sortOrder: 'desc',
      ...(Object.keys(toolFilters).length && {
        entity_type_filters: { tool: toolFilters }
      })
    };
    
    onFiltersChange(filters);
  };

  return (
    <div className="entity-filters">
      {/* Rating filters */}
      <div className="filter-group">
        <label>Minimum Rating</label>
        <input
          type="number"
          min="1"
          max="5"
          step="0.1"
          value={rating.min}
          onChange={(e) => setRating(prev => ({ ...prev, min: e.target.value }))}
        />
      </div>

      {/* Sort options */}
      <div className="filter-group">
        <label>Sort By</label>
        <select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
          <option value="createdAt">Created Date</option>
          <option value="averageRating">Rating</option>
          <option value="popularity">Popularity</option>
          <option value="reviewCount">Review Count</option>
          <option value="name">Name</option>
        </select>
      </div>

      {/* Tool-specific filters */}
      <div className="filter-group">
        <label>
          <input
            type="checkbox"
            checked={toolFilters.has_free_tier || false}
            onChange={(e) => setToolFilters(prev => ({
              ...prev,
              has_free_tier: e.target.checked
            }))}
          />
          Has Free Tier
        </label>
      </div>

      <button onClick={handleApplyFilters}>Apply Filters</button>
    </div>
  );
};
```

## 🎯 Best Practices

### 1. **Performance Optimization**
```javascript
// Use debouncing for search inputs
import { debounce } from 'lodash';

const debouncedSearch = debounce((searchTerm) => {
  updateFilters({ searchTerm, page: 1 });
}, 300);

// Implement pagination for large result sets
const loadMore = () => {
  updateFilters({ page: filters.page + 1 });
};
```

### 2. **URL State Management**
```javascript
// Sync filters with URL for bookmarkable searches
const useUrlFilters = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  
  const filters = useMemo(() => {
    const params = Object.fromEntries(searchParams);
    if (params.entity_type_filters) {
      params.entity_type_filters = JSON.parse(params.entity_type_filters);
    }
    return params;
  }, [searchParams]);

  const updateFilters = (newFilters) => {
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (key === 'entity_type_filters') {
        params.set(key, JSON.stringify(value));
      } else if (value) {
        params.set(key, value);
      }
    });
    setSearchParams(params);
  };

  return { filters, updateFilters };
};
```

### 3. **Error Handling**
```javascript
const fetchEntities = async () => {
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Entity fetch failed:', error);
    // Show user-friendly error message
    throw new Error('Failed to load entities. Please try again.');
  }
};
```

## 🌟 Advanced Use Cases

### 1. **Smart Recommendations**
```javascript
// Find similar entities based on current selection
const findSimilar = async (entityId) => {
  const entity = await fetch(`/entities/${entityId}`).then(r => r.json());
  
  const similarFilters = {
    categoryIds: entity.categories?.map(c => c.id),
    tagIds: entity.tags?.slice(0, 3).map(t => t.id),
    rating_min: Math.max(1, entity.averageRating - 1),
    sortBy: 'relevance',
    limit: 10
  };
  
  return fetch(`/entities?${new URLSearchParams(similarFilters)}`);
};
```

### 2. **Saved Searches**
```javascript
const saveSearch = (name, filters) => {
  const savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
  savedSearches.push({ name, filters, createdAt: new Date().toISOString() });
  localStorage.setItem('savedSearches', JSON.stringify(savedSearches));
};

const loadSavedSearch = (searchName) => {
  const savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
  const search = savedSearches.find(s => s.name === searchName);
  return search?.filters;
};
```

### 3. **Filter Analytics**
```javascript
// Track popular filter combinations
const trackFilterUsage = (filters) => {
  analytics.track('entity_filters_applied', {
    filter_count: Object.keys(filters).length,
    has_entity_type_filters: !!filters.entity_type_filters,
    sort_by: filters.sortBy,
    has_rating_filter: !!(filters.rating_min || filters.rating_max)
  });
};
```

## 🚀 Next Steps

1. **Implement progressive disclosure** - Show basic filters first, advanced on demand
2. **Add filter presets** - "Popular AI Tools", "Free Resources", "Highly Rated"
3. **Create filter suggestions** - Based on user behavior and popular combinations
4. **Implement real-time updates** - Show result counts as users adjust filters
5. **Add export functionality** - Let users export filtered results

## 📞 Support

For questions about the filtering API:
- Check the OpenAPI documentation at `/api-docs`
- Review response schemas for available fields
- Test filters using the interactive API explorer

**Your filtering system is now world-class! 🌍🏆**

---

## 📋 Complete Filter Reference

### All Available Entity Type Filters

#### Software Filters
```javascript
software: {
  license_types: ['MIT', 'Apache 2.0', 'GPL', 'Commercial'],
  programming_languages: ['Python', 'JavaScript', 'Java', 'C++'],
  platform_compatibility: ['Windows', 'macOS', 'Linux', 'Web'],
  open_source: true,
  has_repository: true,
  current_version: '2.0',
  release_date_from: '2023-01-01',
  release_date_to: '2024-12-31',
  languages_search: 'python',
  platforms_search: 'linux',
  license_search: 'MIT'
}
```

#### Podcast Filters
```javascript
podcast: {
  host: 'Lex Fridman',
  main_topics: ['AI', 'Machine Learning', 'Deep Learning'],
  frequency: ['Weekly', 'Bi-weekly', 'Monthly'],
  average_length: '60 minutes',
  has_spotify: true,
  has_apple_podcasts: true,
  has_google_podcasts: true,
  has_youtube: true,
  topics_search: 'machine learning',
  frequency_search: 'weekly'
}
```

#### Book Filters
```javascript
book: {
  author: 'Stuart Russell',
  publisher: 'MIT Press',
  isbn: '978-0262039',
  formats: ['Hardcover', 'Paperback', 'eBook', 'Audiobook'],
  publication_date_from: '2020-01-01',
  publication_date_to: '2024-12-31',
  page_count_min: 200,
  page_count_max: 1000,
  has_purchase_url: true,
  summary_search: 'artificial intelligence',
  format_search: 'ebook'
}
```

#### Community Filters
```javascript
community: {
  community_types: ['Discord', 'Slack', 'Reddit', 'Forum'],
  focus_areas: ['Machine Learning', 'AI Research', 'Startups'],
  target_audience: ['Developers', 'Researchers', 'Students'],
  member_count_min: 1000,
  member_count_max: 100000,
  is_free: true,
  requires_invitation: false,
  has_events: true,
  focus_areas_search: 'machine learning',
  audience_search: 'developers',
  type_search: 'discord'
}
```

#### Grant Filters
```javascript
grant: {
  funding_organizations: ['NSF', 'NIH', 'DARPA', 'EU Horizon'],
  research_areas: ['AI Safety', 'Machine Learning', 'Robotics'],
  eligible_applicants: ['Universities', 'Startups', 'Non-profits'],
  funding_amount_min: 50,    // In thousands
  funding_amount_max: 1000,
  deadline_from: '2024-01-01',
  deadline_to: '2024-12-31',
  is_open: true,
  supports_international: true,
  research_areas_search: 'machine learning',
  organization_search: 'nsf',
  applicants_search: 'startup'
}
```

#### Newsletter Filters
```javascript
newsletter: {
  frequency: ['Daily', 'Weekly', 'Bi-weekly', 'Monthly'],
  focus_areas: ['AI News', 'Research', 'Industry Updates'],
  target_audience: ['Developers', 'Researchers', 'Business Leaders'],
  subscriber_count_min: 1000,
  subscriber_count_max: 100000,
  is_free: true,
  has_archives: true,
  author: 'Andrew Ng',
  focus_areas_search: 'machine learning',
  audience_search: 'developers',
  frequency_search: 'weekly'
}
```

#### Hardware Filters
```javascript
hardware: {
  device_types: ['GPU', 'CPU', 'TPU', 'FPGA'],
  manufacturers: ['NVIDIA', 'AMD', 'Intel', 'Google'],
  performance_tiers: ['Entry', 'Mid-range', 'High-end', 'Enterprise'],
  memory_min: 8,     // In GB
  memory_max: 128,
  price_min: 100,    // In USD
  price_max: 5000,
  power_consumption_max: 300,  // In watts
  release_year_min: 2020,
  release_year_max: 2024,
  cuda_support: true,
  specifications_search: 'tensor cores',
  use_cases_search: 'deep learning'
}
```

#### Event Filters
```javascript
event: {
  event_types: ['Conference', 'Workshop', 'Meetup', 'Webinar'],
  formats: ['In-person', 'Virtual', 'Hybrid'],
  topics: ['Machine Learning', 'AI Ethics', 'Computer Vision'],
  target_audiences: ['Researchers', 'Industry', 'Students'],
  start_date_from: '2024-01-01',
  start_date_to: '2024-12-31',
  duration_days_min: 1,
  duration_days_max: 5,
  price_min: 0,
  price_max: 2000,
  location_search: 'San Francisco',
  is_free: true,
  has_recordings: true,
  registration_required: true,
  topics_search: 'nlp',
  speakers_search: 'Geoffrey Hinton'
}
```

### Response Format
```javascript
{
  "data": [
    {
      "id": "uuid",
      "name": "Entity Name",
      "description": "Entity description",
      "averageRating": 4.5,
      "reviewCount": 25,
      "saveCount": 150,
      "entityType": {
        "id": "uuid",
        "name": "Tool",
        "slug": "tool"
      },
      "categories": [...],
      "tags": [...],
      // Entity type specific details
      "entityDetailsTool": {
        "technicalLevel": "INTERMEDIATE",
        "hasFreeTier": true,
        "apiAccess": true,
        // ... other tool-specific fields
      }
    }
  ],
  "total": 1250,
  "pagination": {
    "page": 1,
    "limit": 20,
    "totalPages": 63,
    "hasNext": true,
    "hasPrev": false
  }
}
```
