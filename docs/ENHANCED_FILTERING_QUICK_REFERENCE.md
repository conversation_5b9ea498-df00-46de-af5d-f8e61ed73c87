# Enhanced Filtering - Quick Reference Guide

## 🎯 **STATUS: ALL TESTS PASSED - PRODUCTION READY!** ✅

---

## 🚀 **QUICK START**

### **Basic Usage**
```javascript
// Simple course filter
fetch('/entities?entity_type_filters[course][skill_levels]=BEGINNER,INTERMEDIATE')

// Simple job filter  
fetch('/entities?entity_type_filters[job][location_types]=Remote&entity_type_filters[job][salary_min]=80')

// Combined with existing filters
fetch('/entities?status=ACTIVE&entity_type_filters[course][certificate_available]=true')
```

---

## 📋 **FILTER REFERENCE**

### **🎓 Course Filters**
| Filter | Type | Example Values |
|--------|------|----------------|
| `skill_levels` | Array | `['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']` |
| `certificate_available` | Boolean | `true`, `false` |
| `instructor_name` | String | `'Dr. <PERSON>'` |
| `duration_text` | String | `'8 weeks'` |
| `enrollment_min` | Number | `100` |
| `enrollment_max` | Number | `10000` |
| `prerequisites` | String | `'Python programming'` |
| `has_syllabus` | Boolean | `true`, `false` |

### **💼 Job Filters**
| Filter | Type | Example Values |
|--------|------|----------------|
| `employment_types` | Array | `['Full-time', 'Part-time', 'Contract']` |
| `experience_levels` | Array | `['Entry', 'Mid', 'Senior']` |
| `location_types` | Array | `['Remote', 'On-site', 'Hybrid']` |
| `company_name` | String | `'TechCorp'` |
| `job_title` | String | `'AI Engineer'` |
| `salary_min` | Number | `80` (in thousands) |
| `salary_max` | Number | `150` (in thousands) |
| `job_description` | String | `'machine learning'` |
| `has_application_url` | Boolean | `true`, `false` |

### **🖥️ Hardware Filters**
| Filter | Type | Example Values |
|--------|------|----------------|
| `gpu_search` | String | `'NVIDIA RTX 4090'` |
| `processor_search` | String | `'Intel i9'` |
| `memory_search` | String | `'64GB'` |
| `storage_search` | String | `'2TB SSD'` |
| `price_search` | String | `'$3,999'` |
| `availability_search` | String | `'In Stock'` |
| `power_consumption_search` | String | `'450W'` |

### **📅 Event Filters**
| Filter | Type | Example Values |
|--------|------|----------------|
| `event_types` | Array | `['Conference', 'Workshop', 'Webinar']` |
| `start_date_from` | String | `'2024-01-01'` |
| `start_date_to` | String | `'2024-12-31'` |
| `end_date_from` | String | `'2024-01-01'` |
| `end_date_to` | String | `'2024-12-31'` |
| `is_online` | Boolean | `true`, `false` |
| `location` | String | `'San Francisco'` |
| `has_registration_url` | Boolean | `true`, `false` |

---

## 🔧 **URL PARAMETER FORMAT**

### **Pattern**
```
entity_type_filters[ENTITY_TYPE][FILTER_NAME]=VALUE
```

### **Examples**
```bash
# Single filter
?entity_type_filters[course][skill_levels]=BEGINNER

# Multiple values (comma-separated)
?entity_type_filters[course][skill_levels]=BEGINNER,INTERMEDIATE

# Multiple filters
?entity_type_filters[course][skill_levels]=BEGINNER&entity_type_filters[course][certificate_available]=true

# Multiple entity types
?entity_type_filters[course][skill_levels]=BEGINNER&entity_type_filters[job][location_types]=Remote
```

---

## 💻 **JAVASCRIPT HELPERS**

### **Build Query Parameters**
```javascript
function buildEntityTypeFilters(filters) {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([entityType, entityFilters]) => {
    Object.entries(entityFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        const paramKey = `entity_type_filters[${entityType}][${key}]`;
        
        if (Array.isArray(value)) {
          params.set(paramKey, value.join(','));
        } else {
          params.set(paramKey, value.toString());
        }
      }
    });
  });
  
  return params;
}

// Usage
const filters = {
  course: {
    skill_levels: ['BEGINNER', 'INTERMEDIATE'],
    certificate_available: true
  },
  job: {
    location_types: ['Remote'],
    salary_min: 80
  }
};

const params = buildEntityTypeFilters(filters);
const url = `/entities?${params}`;
```

### **React Hook**
```javascript
function useEntityTypeFilters() {
  const [filters, setFilters] = useState({
    course: {},
    job: {},
    hardware: {},
    event: {}
  });
  
  const updateFilter = (entityType, key, value) => {
    setFilters(prev => ({
      ...prev,
      [entityType]: {
        ...prev[entityType],
        [key]: value
      }
    }));
  };
  
  const clearEntityFilters = (entityType) => {
    setFilters(prev => ({
      ...prev,
      [entityType]: {}
    }));
  };
  
  const getQueryParams = () => buildEntityTypeFilters(filters);
  
  return { filters, updateFilter, clearEntityFilters, getQueryParams };
}
```

---

## 🎨 **UI COMPONENT EXAMPLES**

### **Multi-Select Component**
```jsx
function MultiSelectFilter({ label, options, value, onChange }) {
  return (
    <div className="filter-group">
      <label>{label}</label>
      <select 
        multiple 
        value={value || []}
        onChange={(e) => {
          const selected = Array.from(e.target.selectedOptions, option => option.value);
          onChange(selected);
        }}
      >
        {options.map(option => (
          <option key={option} value={option}>{option}</option>
        ))}
      </select>
    </div>
  );
}
```

### **Range Filter Component**
```jsx
function RangeFilter({ label, min, max, value, onChange }) {
  return (
    <div className="filter-group">
      <label>{label}</label>
      <div className="range-inputs">
        <input
          type="number"
          placeholder="Min"
          value={value?.min || ''}
          onChange={(e) => onChange({ ...value, min: parseInt(e.target.value) })}
        />
        <input
          type="number"
          placeholder="Max"
          value={value?.max || ''}
          onChange={(e) => onChange({ ...value, max: parseInt(e.target.value) })}
        />
      </div>
    </div>
  );
}
```

### **Boolean Filter Component**
```jsx
function BooleanFilter({ label, value, onChange }) {
  return (
    <div className="filter-group">
      <label>
        <input
          type="checkbox"
          checked={value || false}
          onChange={(e) => onChange(e.target.checked)}
        />
        {label}
      </label>
    </div>
  );
}
```

---

## 🧪 **TESTING**

### **Test Data Available**
- **2 Course entities** with different skill levels and certificates
- **2 Job entities** with different locations and salary ranges  
- **2 Hardware entities** with different specifications
- **2 Event entities** with different types and formats

### **Test Queries**
```javascript
// Test each entity type
const testQueries = [
  '/entities?entity_type_filters[course][skill_levels]=BEGINNER',
  '/entities?entity_type_filters[job][location_types]=Remote',
  '/entities?entity_type_filters[hardware][gpu_search]=NVIDIA',
  '/entities?entity_type_filters[event][is_online]=true'
];

// Run tests
testQueries.forEach(async (query) => {
  const response = await fetch(query);
  const data = await response.json();
  console.log(`Query: ${query}`);
  console.log(`Results: ${data.total} entities found`);
});
```

---

## ⚡ **PERFORMANCE TIPS**

1. **Debounce text inputs** (300ms recommended)
2. **Batch filter updates** instead of individual API calls
3. **Use pagination** with `limit` and `page` parameters
4. **Cache results** for common filter combinations
5. **Show loading states** during API calls

---

## 🐛 **COMMON ISSUES**

### **No Results**
- Check if filters are too restrictive
- Verify filter values are valid
- Test without filters first

### **Invalid Parameters**
- Ensure proper URL encoding
- Check array values are comma-separated
- Verify boolean values are 'true'/'false' strings

### **Performance Issues**
- Add debouncing to text inputs
- Implement result caching
- Use pagination for large result sets

---

## 📞 **SUPPORT**

- **Backend**: ✅ Fully implemented and tested
- **API Documentation**: See main guide for detailed examples
- **Test Data**: Available for all entity types
- **All Tests**: ✅ PASSING

**Ready for immediate frontend implementation!** 🚀
