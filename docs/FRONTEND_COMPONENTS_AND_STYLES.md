# Enhanced Filtering - Components & Styles

## 🧩 **UTILITY COMPONENTS**

### **MultiSelect Component**
```jsx
import React, { useState, useRef, useEffect } from 'react';

function MultiSelect({ options, value = [], onChange, placeholder = "Select options..." }) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  // Filter options based on search
  const filteredOptions = options.filter(option =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const toggleOption = (option) => {
    const newValue = value.includes(option)
      ? value.filter(v => v !== option)
      : [...value, option];
    onChange(newValue);
  };
  
  const selectAll = () => {
    onChange(filteredOptions);
  };
  
  const clearAll = () => {
    onChange([]);
  };
  
  return (
    <div className="multi-select" ref={dropdownRef}>
      <div 
        className={`multi-select-trigger ${isOpen ? 'open' : ''}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="selected-text">
          {value.length === 0 
            ? placeholder 
            : `${value.length} selected`
          }
        </span>
        <span className="arrow">▼</span>
      </div>
      
      {isOpen && (
        <div className="multi-select-dropdown">
          {/* Search */}
          <div className="search-container">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search options..."
              className="search-input"
            />
          </div>
          
          {/* Actions */}
          <div className="actions">
            <button onClick={selectAll} className="action-btn">
              Select All ({filteredOptions.length})
            </button>
            <button onClick={clearAll} className="action-btn">
              Clear All
            </button>
          </div>
          
          {/* Options */}
          <div className="options-list">
            {filteredOptions.map(option => (
              <label key={option} className="option-item">
                <input
                  type="checkbox"
                  checked={value.includes(option)}
                  onChange={() => toggleOption(option)}
                />
                <span className="option-text">{option}</span>
              </label>
            ))}
          </div>
          
          {filteredOptions.length === 0 && (
            <div className="no-options">No options found</div>
          )}
        </div>
      )}
      
      {/* Selected Values Display */}
      {value.length > 0 && (
        <div className="selected-values">
          {value.map(val => (
            <span key={val} className="selected-tag">
              {val}
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  toggleOption(val);
                }}
                className="remove-tag"
              >
                ×
              </button>
            </span>
          ))}
        </div>
      )}
    </div>
  );
}

export { MultiSelect };
```

### **Hardware & Event Filter Components**
```jsx
// Hardware Filters Component
function HardwareFilters({ filters, onChange, onClear }) {
  return (
    <div className="hardware-filters">
      <div className="filter-header">
        <h3>Hardware Filters</h3>
        <button onClick={onClear} className="clear-filters">Clear All</button>
      </div>
      
      <div className="filter-group">
        <label>GPU Search</label>
        <input
          type="text"
          value={filters.gpu_search || ''}
          onChange={(e) => onChange('gpu_search', e.target.value)}
          placeholder="e.g., NVIDIA RTX 4090, AMD RX 7900"
        />
      </div>
      
      <div className="filter-group">
        <label>Processor Search</label>
        <input
          type="text"
          value={filters.processor_search || ''}
          onChange={(e) => onChange('processor_search', e.target.value)}
          placeholder="e.g., Intel i9, AMD Ryzen"
        />
      </div>
      
      <div className="filter-group">
        <label>Memory Search</label>
        <input
          type="text"
          value={filters.memory_search || ''}
          onChange={(e) => onChange('memory_search', e.target.value)}
          placeholder="e.g., 64GB, 128GB DDR5"
        />
      </div>
      
      <div className="filter-group">
        <label>Storage Search</label>
        <input
          type="text"
          value={filters.storage_search || ''}
          onChange={(e) => onChange('storage_search', e.target.value)}
          placeholder="e.g., 2TB SSD, NVMe"
        />
      </div>
      
      <div className="filter-group">
        <label>Price Search</label>
        <input
          type="text"
          value={filters.price_search || ''}
          onChange={(e) => onChange('price_search', e.target.value)}
          placeholder="e.g., $3,999, under $5000"
        />
      </div>
      
      <div className="filter-group">
        <label>Availability</label>
        <input
          type="text"
          value={filters.availability_search || ''}
          onChange={(e) => onChange('availability_search', e.target.value)}
          placeholder="e.g., In Stock, Available"
        />
      </div>
      
      <div className="filter-group">
        <label>Power Consumption</label>
        <input
          type="text"
          value={filters.power_consumption_search || ''}
          onChange={(e) => onChange('power_consumption_search', e.target.value)}
          placeholder="e.g., 450W, under 300W"
        />
      </div>
    </div>
  );
}

// Event Filters Component
function EventFilters({ filters, onChange, onClear }) {
  const eventTypes = ['Conference', 'Workshop', 'Webinar', 'Meetup', 'Hackathon'];
  
  return (
    <div className="event-filters">
      <div className="filter-header">
        <h3>Event Filters</h3>
        <button onClick={onClear} className="clear-filters">Clear All</button>
      </div>
      
      <div className="filter-group">
        <label>Event Type</label>
        <MultiSelect
          options={eventTypes}
          value={filters.event_types || []}
          onChange={(values) => onChange('event_types', values)}
          placeholder="Select event types..."
        />
      </div>
      
      <div className="filter-group">
        <label>Start Date Range</label>
        <div className="date-range">
          <input
            type="date"
            value={filters.start_date_from || ''}
            onChange={(e) => onChange('start_date_from', e.target.value)}
            placeholder="From"
          />
          <span>to</span>
          <input
            type="date"
            value={filters.start_date_to || ''}
            onChange={(e) => onChange('start_date_to', e.target.value)}
            placeholder="To"
          />
        </div>
      </div>
      
      <div className="filter-group">
        <label>End Date Range</label>
        <div className="date-range">
          <input
            type="date"
            value={filters.end_date_from || ''}
            onChange={(e) => onChange('end_date_from', e.target.value)}
            placeholder="From"
          />
          <span>to</span>
          <input
            type="date"
            value={filters.end_date_to || ''}
            onChange={(e) => onChange('end_date_to', e.target.value)}
            placeholder="To"
          />
        </div>
      </div>
      
      <div className="filter-group">
        <label className="checkbox-label">
          <input
            type="checkbox"
            checked={filters.is_online || false}
            onChange={(e) => onChange('is_online', e.target.checked)}
          />
          Online Event
        </label>
      </div>
      
      <div className="filter-group">
        <label>Location</label>
        <input
          type="text"
          value={filters.location || ''}
          onChange={(e) => onChange('location', e.target.value)}
          placeholder="e.g., San Francisco, Online"
        />
      </div>
      
      <div className="filter-group">
        <label className="checkbox-label">
          <input
            type="checkbox"
            checked={filters.has_registration_url || false}
            onChange={(e) => onChange('has_registration_url', e.target.checked)}
          />
          Registration Available
        </label>
      </div>
    </div>
  );
}

export { HardwareFilters, EventFilters };
```

### **Utility Hooks**
```javascript
// useDebounce Hook
import { useState, useEffect } from 'react';

function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
}

// useLocalStorage Hook for filter persistence
function useLocalStorage(key, initialValue) {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });
  
  const setValue = (value) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };
  
  return [storedValue, setValue];
}

// useUrlParams Hook for URL synchronization
function useUrlParams() {
  const [searchParams, setSearchParams] = useSearchParams();
  
  const updateUrlParams = useCallback((filters) => {
    const params = new URLSearchParams();
    
    // Add basic search params
    const currentSearch = searchParams.get('searchTerm');
    if (currentSearch) params.set('searchTerm', currentSearch);
    
    // Add enhanced filter params
    Object.entries(filters).forEach(([entityType, entityFilters]) => {
      Object.entries(entityFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          const paramKey = `entity_type_filters[${entityType}][${key}]`;
          
          if (Array.isArray(value)) {
            params.set(paramKey, value.join(','));
          } else {
            params.set(paramKey, value.toString());
          }
        }
      });
    });
    
    setSearchParams(params);
  }, [searchParams, setSearchParams]);
  
  const getFiltersFromUrl = useCallback(() => {
    const filters = { course: {}, job: {}, hardware: {}, event: {} };
    
    for (const [key, value] of searchParams.entries()) {
      const match = key.match(/entity_type_filters\[(\w+)\]\[(\w+)\]/);
      if (match) {
        const [, entityType, filterKey] = match;
        if (filters[entityType]) {
          // Handle arrays (comma-separated values)
          if (value.includes(',')) {
            filters[entityType][filterKey] = value.split(',');
          } else if (value === 'true' || value === 'false') {
            // Handle booleans
            filters[entityType][filterKey] = value === 'true';
          } else if (!isNaN(value)) {
            // Handle numbers
            filters[entityType][filterKey] = parseInt(value);
          } else {
            // Handle strings
            filters[entityType][filterKey] = value;
          }
        }
      }
    }
    
    return filters;
  }, [searchParams]);
  
  return { updateUrlParams, getFiltersFromUrl };
}

export { useDebounce, useLocalStorage, useUrlParams };
```

### **Pagination Component**
```jsx
function Pagination({ currentPage, totalPages, onPageChange }) {
  const getPageNumbers = () => {
    const pages = [];
    const maxVisible = 5;
    
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(totalPages, start + maxVisible - 1);
    
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    return pages;
  };
  
  if (totalPages <= 1) return null;
  
  return (
    <div className="pagination">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="page-btn prev"
      >
        Previous
      </button>
      
      {currentPage > 3 && (
        <>
          <button onClick={() => onPageChange(1)} className="page-btn">1</button>
          {currentPage > 4 && <span className="ellipsis">...</span>}
        </>
      )}
      
      {getPageNumbers().map(page => (
        <button
          key={page}
          onClick={() => onPageChange(page)}
          className={`page-btn ${page === currentPage ? 'active' : ''}`}
        >
          {page}
        </button>
      ))}
      
      {currentPage < totalPages - 2 && (
        <>
          {currentPage < totalPages - 3 && <span className="ellipsis">...</span>}
          <button onClick={() => onPageChange(totalPages)} className="page-btn">
            {totalPages}
          </button>
        </>
      )}
      
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="page-btn next"
      >
        Next
      </button>
    </div>
  );
}

export { Pagination };
```

---

## 🎨 **CSS STYLES**

### **Enhanced Filters Styles**
```css
/* Enhanced Filters Container */
.enhanced-filters {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: white;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Filter Tabs */
.filter-tabs {
  display: flex;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.filter-tab {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.filter-tab:hover {
  background: #e9ecef;
  color: #495057;
}

.filter-tab.active {
  background: white;
  color: #007bff;
  border-bottom: 2px solid #007bff;
}

.filter-count {
  font-size: 12px;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 10px;
  color: #6c757d;
  font-weight: normal;
}

.filter-tab.active .filter-count {
  background: #e3f2fd;
  color: #1976d2;
}

/* Filter Content */
.filter-content {
  padding: 20px;
  max-height: 500px;
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.filter-header h3 {
  margin: 0;
  color: #212529;
  font-size: 18px;
}

.clear-filters {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s;
}

.clear-filters:hover {
  background: #c82333;
}

/* Filter Groups */
.filter-group {
  margin-bottom: 20px;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.filter-group input[type="text"],
.filter-group input[type="number"],
.filter-group input[type="date"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-group small {
  display: block;
  margin-top: 4px;
  color: #6c757d;
  font-size: 12px;
}

/* Range Inputs */
.range-inputs,
.date-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-inputs input,
.date-range input {
  flex: 1;
}

.range-inputs span,
.date-range span {
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

/* Checkbox Labels */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  margin-bottom: 0 !important;
  padding: 8px 0;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  width: 16px;
  height: 16px;
}

/* Multi-Select Styles */
.multi-select {
  position: relative;
}

.multi-select-trigger {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  min-height: 38px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.multi-select-trigger:hover {
  border-color: #adb5bd;
}

.multi-select-trigger.open {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.selected-text {
  color: #495057;
  font-size: 14px;
}

.arrow {
  color: #6c757d;
  font-size: 12px;
  transition: transform 0.2s;
}

.multi-select-trigger.open .arrow {
  transform: rotate(180deg);
}

.multi-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ced4da;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.search-container {
  padding: 8px;
  border-bottom: 1px solid #e9ecef;
}

.search-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
}

.actions {
  display: flex;
  gap: 8px;
  padding: 8px;
  border-bottom: 1px solid #e9ecef;
}

.action-btn {
  flex: 1;
  padding: 4px 8px;
  border: 1px solid #ced4da;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s;
}

.action-btn:hover {
  background: #f8f9fa;
}

.options-list {
  max-height: 200px;
  overflow-y: auto;
}

.option-item {
  display: flex !important;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  margin: 0 !important;
  transition: background 0.2s;
}

.option-item:hover {
  background: #f8f9fa;
}

.option-item input[type="checkbox"] {
  margin: 0;
  width: 14px;
  height: 14px;
}

.option-text {
  font-size: 14px;
  color: #495057;
}

.no-options {
  padding: 12px;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
}

.selected-values {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.remove-tag {
  background: none;
  border: none;
  color: #1976d2;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s;
}

.remove-tag:hover {
  background: #1976d2;
  color: white;
}

/* Search Results */
.search-results {
  margin-top: 24px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.results-header h3 {
  margin: 0;
  color: #212529;
  font-size: 18px;
}

.results-header select {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  font-size: 14px;
}

.entities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

/* Filter Summary */
.filter-summary {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: #e3f2fd;
  border-radius: 4px;
  font-size: 14px;
  color: #1976d2;
  margin-top: 12px;
}

.clear-all {
  background: #1976d2;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s;
}

.clear-all:hover {
  background: #1565c0;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  background: white;
  color: #495057;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
}

.page-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ellipsis {
  padding: 8px 4px;
  color: #6c757d;
}

/* Loading and Error States */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  color: #6c757d;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 16px;
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  margin-bottom: 16px;
}

.error-message button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.empty-state h3 {
  margin-bottom: 8px;
  color: #495057;
}

.empty-state button {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}
```
