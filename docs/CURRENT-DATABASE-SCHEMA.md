# Current Database Schema

**Generated:** 2025-06-22T08:48:43.956Z
**Tables:** 47

## Summary

This document reflects the current state of the database schema.

## Tables Overview

1. **_prisma_migrations** (8 columns)
2. **app_settings** (5 columns)
3. **badge_types** (8 columns)
4. **categories** (8 columns)
5. **entities** (32 columns)
6. **entity_badges** (7 columns)
7. **entity_categories** (4 columns)
8. **entity_details_agency** (9 columns)
9. **entity_details_book** (12 columns)
10. **entity_details_bounty** (11 columns)
11. **entity_details_community** (8 columns)
12. **entity_details_content_creator** (7 columns)
13. **entity_details_course** (9 columns)
14. **entity_details_dataset** (12 columns)
15. **entity_details_event** (14 columns)
16. **entity_details_grant** (12 columns)
17. **entity_details_hardware** (12 columns)
18. **entity_details_investor** (12 columns)
19. **entity_details_job** (14 columns)
20. **entity_details_model** (15 columns)
21. **entity_details_news** (10 columns)
22. **entity_details_newsletter** (8 columns)
23. **entity_details_platform** (28 columns)
24. **entity_details_podcast** (12 columns)
25. **entity_details_project_reference** (12 columns)
26. **entity_details_research_paper** (11 columns)
27. **entity_details_service_provider** (11 columns)
28. **entity_details_software** (32 columns)
29. **entity_details_tool** (30 columns)
30. **entity_features** (5 columns)
31. **entity_tags** (5 columns)
32. **entity_types** (7 columns)
33. **features** (7 columns)
34. **profile_activities** (8 columns)
35. **review_votes** (5 columns)
36. **reviews** (13 columns)
37. **tags** (6 columns)
38. **tool_requests** (13 columns)
39. **user_activity_logs** (10 columns)
40. **user_badges** (6 columns)
41. **user_followed_categories** (3 columns)
42. **user_followed_tags** (4 columns)
43. **user_notification_settings** (10 columns)
44. **user_preferences** (18 columns)
45. **user_saved_entities** (4 columns)
46. **user_submitted_tools** (9 columns)
47. **users** (56 columns)

## Detailed Schema

### _prisma_migrations

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | character varying | NO | - |
| checksum | character varying | NO | - |
| finished_at | timestamp with time zone | YES | - |
| migration_name | character varying | NO | - |
| logs | text | YES | - |
| rolled_back_at | timestamp with time zone | YES | - |
| started_at | timestamp with time zone | NO | now() |
| applied_steps_count | integer | NO | 0 |

### app_settings

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| key | text | NO | - |
| value | text | NO | - |
| description | text | YES | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |

### badge_types

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| name | text | NO | - |
| description | text | YES | - |
| icon_url | text | YES | - |
| scope | USER-DEFINED | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| criteria | jsonb | YES | - |

### categories

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| name | text | NO | - |
| description | text | YES | - |
| slug | text | NO | - |
| icon_url | text | YES | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| parent_id | uuid | YES | - |

### entities

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| entity_type_id | uuid | NO | - |
| name | text | NO | - |
| short_description | text | YES | - |
| description | text | YES | - |
| logo_url | text | YES | - |
| website_url | text | YES | - |
| documentation_url | text | YES | - |
| contact_url | text | YES | - |
| privacy_policy_url | text | YES | - |
| founded_year | integer | YES | - |
| social_links | jsonb | YES | - |
| status | USER-DEFINED | NO | 'PENDING'::"EntityStatus" |
| avg_rating | double precision | NO | 0 |
| review_count | integer | NO | 0 |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| legacy_id | text | YES | - |
| submitter_id | uuid | NO | - |
| affiliateStatus | USER-DEFINED | YES | 'NONE'::"AffiliateStatus" |
| location_summary | text | YES | - |
| meta_description | text | YES | - |
| meta_title | text | YES | - |
| ref_link | text | YES | - |
| scraped_review_count | integer | YES | - |
| scraped_review_sentiment_label | text | YES | - |
| scraped_review_sentiment_score | double precision | YES | - |
| vector_embedding | USER-DEFINED | YES | - |
| ftsDocument | tsvector | YES | - |
| employee_count_range | USER-DEFINED | YES | - |
| funding_stage | USER-DEFINED | YES | - |
| slug | text | NO | - |

### entity_badges

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| entity_id | uuid | NO | - |
| badge_type_id | uuid | NO | - |
| granted_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| notes | text | YES | - |
| expires_at | timestamp without time zone | YES | - |
| granted_by | uuid | YES | - |

### entity_categories

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| category_id | uuid | NO | - |
| assigned_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| assigned_by | uuid | NO | - |

### entity_details_agency

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| services_offered | jsonb | YES | - |
| industry_focus | jsonb | YES | - |
| target_client_size | jsonb | YES | - |
| target_audience | jsonb | YES | - |
| location_summary | text | YES | - |
| portfolio_url | text | YES | - |
| pricing_info | text | YES | - |
| id | uuid | NO | - |

### entity_details_book

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| isbn | text | YES | - |
| page_count | integer | YES | - |
| publisher | text | YES | - |
| purchase_url | text | YES | - |
| summary | text | YES | - |
| author | text | YES | - |
| format | text | YES | - |
| id | uuid | NO | - |
| publication_date | timestamp without time zone | YES | - |

### entity_details_bounty

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| amount | text | YES | - |
| deadline | timestamp without time zone | YES | - |
| id | uuid | NO | - |
| platform | text | YES | - |
| required_skills | ARRAY | YES | - |
| status | text | YES | - |
| task_description | text | YES | - |
| url | text | YES | - |

### entity_details_community

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| platform | text | YES | - |
| member_count | integer | YES | - |
| focus_topics | jsonb | YES | - |
| rules_url | text | YES | - |
| invite_url | text | YES | - |
| main_channel_url | text | YES | - |
| id | uuid | NO | - |

### entity_details_content_creator

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| creator_name | text | YES | - |
| primary_platform | text | YES | - |
| focus_areas | jsonb | YES | - |
| follower_count | integer | YES | - |
| example_content_url | text | YES | - |
| id | uuid | NO | - |

### entity_details_course

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| instructor_name | text | YES | - |
| duration_text | text | YES | - |
| skill_level | USER-DEFINED | YES | - |
| prerequisites | text | YES | - |
| syllabus_url | text | YES | - |
| enrollment_count | integer | YES | - |
| certificate_available | boolean | YES | false |
| id | uuid | NO | - |

### entity_details_dataset

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| access_notes | text | YES | - |
| description | text | YES | - |
| license | text | YES | - |
| size_in_bytes | bigint | YES | - |
| source_url | text | YES | - |
| collection_method | text | YES | - |
| id | uuid | NO | - |
| update_frequency | text | YES | - |
| format | text | YES | - |

### entity_details_event

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| end_date | timestamp without time zone | YES | - |
| location | text | YES | - |
| price | text | YES | - |
| registration_url | text | YES | - |
| start_date | timestamp without time zone | YES | - |
| event_type | text | YES | - |
| id | uuid | NO | - |
| is_online | boolean | YES | - |
| key_speakers | ARRAY | YES | - |
| target_audience | ARRAY | YES | - |
| topics | ARRAY | YES | - |

### entity_details_grant

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| application_url | text | YES | - |
| amount | text | YES | - |
| deadline | timestamp without time zone | YES | - |
| eligibility | text | YES | - |
| focus_areas | ARRAY | YES | - |
| funder_name | text | YES | - |
| grant_type | text | YES | - |
| id | uuid | NO | - |
| location | text | YES | - |

### entity_details_hardware

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| availability | text | YES | - |
| gpu | text | YES | - |
| id | uuid | NO | - |
| memory | text | YES | - |
| power_consumption | text | YES | - |
| price | text | YES | - |
| processor | text | YES | - |
| storage | text | YES | - |
| use_cases | ARRAY | YES | - |

### entity_details_investor

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| contact_email | text | YES | - |
| application_url | text | YES | - |
| focus_areas | ARRAY | YES | - |
| id | uuid | NO | - |
| investment_stages | ARRAY | YES | - |
| investor_type | text | YES | - |
| location_summary | text | YES | - |
| notable_investments | ARRAY | YES | - |
| portfolio_size | integer | YES | - |

### entity_details_job

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| application_url | text | YES | - |
| company_name | text | YES | - |
| experience_level | text | YES | - |
| id | uuid | NO | - |
| is_remote | boolean | YES | - |
| job_type | text | YES | - |
| key_responsibilities | ARRAY | YES | - |
| location | text | YES | - |
| required_skills | ARRAY | YES | - |
| salary_max | double precision | YES | - |
| salary_min | double precision | YES | - |

### entity_details_model

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| license | text | YES | - |
| model_architecture | text | YES | - |
| training_dataset | text | YES | - |
| deployment_options | ARRAY | YES | - |
| frameworks | ARRAY | YES | - |
| id | uuid | NO | - |
| input_data_types | ARRAY | YES | - |
| libraries | ARRAY | YES | - |
| output_data_types | ARRAY | YES | - |
| performance_metrics | jsonb | YES | - |
| target_audience | ARRAY | YES | - |
| use_cases | ARRAY | YES | - |

### entity_details_news

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| article_url | text | YES | - |
| author | text | YES | - |
| publication_date | timestamp without time zone | YES | - |
| source_name | text | YES | - |
| summary | text | YES | - |
| id | uuid | NO | - |
| tags | ARRAY | YES | - |

### entity_details_newsletter

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| frequency | text | YES | - |
| main_topics | jsonb | YES | - |
| archive_url | text | YES | - |
| subscribe_url | text | YES | - |
| author_name | text | YES | - |
| subscriber_count | integer | YES | 0 |
| id | uuid | NO | - |

### entity_details_platform

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| documentation_url | text | YES | - |
| platform_type | text | YES | - |
| community_url | text | YES | - |
| has_free_tier | boolean | YES | - |
| has_live_chat | boolean | YES | - |
| price_range | USER-DEFINED | YES | - |
| pricing_details | text | YES | - |
| pricing_url | text | YES | - |
| support_email | text | YES | - |
| pricing_model | USER-DEFINED | YES | - |
| api_access | boolean | YES | - |
| customization_level | text | YES | - |
| demo_available | boolean | YES | - |
| deployment_options | ARRAY | YES | - |
| has_api | boolean | YES | - |
| id | uuid | NO | - |
| mobile_support | boolean | YES | - |
| open_source | boolean | YES | - |
| support_channels | ARRAY | YES | - |
| supported_os | ARRAY | YES | - |
| target_audience | ARRAY | YES | - |
| trial_available | boolean | YES | - |
| integrations | ARRAY | YES | - |
| key_services | ARRAY | YES | - |
| use_cases | ARRAY | YES | - |

### entity_details_podcast

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| frequency | text | YES | - |
| apple_podcasts_url | text | YES | - |
| average_length | text | YES | - |
| google_podcasts_url | text | YES | - |
| host | text | YES | - |
| id | uuid | NO | - |
| main_topics | ARRAY | YES | - |
| spotify_url | text | YES | - |
| youtube_url | text | YES | - |

### entity_details_project_reference

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| forks | integer | YES | - |
| id | uuid | NO | - |
| key_technologies | ARRAY | YES | - |
| license | text | YES | - |
| repository_url | text | YES | - |
| stars | integer | YES | - |
| status | text | YES | - |
| use_cases | ARRAY | YES | - |
| contributors | integer | YES | - |

### entity_details_research_paper

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| abstract | text | YES | - |
| citation_count | integer | YES | - |
| doi | text | YES | - |
| journal_or_conference | text | YES | - |
| publication_date | timestamp without time zone | YES | - |
| id | uuid | NO | - |
| pdf_url | text | YES | - |
| authors | ARRAY | YES | - |

### entity_details_service_provider

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| company_size_focus | text | YES | - |
| id | uuid | NO | - |
| industry_specializations | ARRAY | YES | - |
| location_summary | text | YES | - |
| portfolio_url | text | YES | - |
| pricing_info | text | YES | - |
| services_offered | ARRAY | YES | - |
| target_audience | ARRAY | YES | - |

### entity_details_software

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| current_version | text | YES | - |
| license_type | text | YES | - |
| community_url | text | YES | - |
| has_free_tier | boolean | YES | - |
| has_live_chat | boolean | YES | - |
| price_range | USER-DEFINED | YES | - |
| pricing_details | text | YES | - |
| pricing_model | USER-DEFINED | YES | - |
| pricing_url | text | YES | - |
| support_email | text | YES | - |
| api_access | boolean | YES | - |
| customization_level | text | YES | - |
| demo_available | boolean | YES | - |
| deployment_options | ARRAY | YES | - |
| frameworks | ARRAY | YES | - |
| has_api | boolean | YES | - |
| id | uuid | NO | - |
| key_features | ARRAY | YES | - |
| libraries | ARRAY | YES | - |
| mobile_support | boolean | YES | - |
| open_source | boolean | YES | - |
| support_channels | ARRAY | YES | - |
| supported_os | ARRAY | YES | - |
| target_audience | ARRAY | YES | - |
| trial_available | boolean | YES | - |
| integrations | ARRAY | YES | - |
| platform_compatibility | ARRAY | YES | - |
| programming_languages | ARRAY | YES | - |
| use_cases | ARRAY | YES | - |

### entity_details_tool

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| learning_curve | USER-DEFINED | YES | - |
| target_audience | jsonb | YES | - |
| key_features | jsonb | YES | - |
| use_cases | jsonb | YES | - |
| pricing_model | USER-DEFINED | YES | - |
| price_range | USER-DEFINED | YES | - |
| pricing_details | text | YES | - |
| pricing_url | text | YES | - |
| has_free_tier | boolean | YES | - |
| integrations | jsonb | YES | - |
| api_access | boolean | YES | - |
| community_url | text | YES | - |
| customization_level | text | YES | - |
| demo_available | boolean | YES | - |
| deployment_options | jsonb | YES | - |
| frameworks | jsonb | YES | - |
| has_live_chat | boolean | YES | - |
| libraries | jsonb | YES | - |
| mobile_support | boolean | YES | - |
| open_source | boolean | YES | - |
| programming_languages | jsonb | YES | - |
| support_channels | jsonb | YES | - |
| support_email | text | YES | - |
| supported_os | jsonb | YES | - |
| trial_available | boolean | YES | - |
| has_api | boolean | YES | - |
| id | uuid | NO | - |
| platforms | jsonb | YES | - |
| technical_level | USER-DEFINED | YES | - |

### entity_features

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| feature_id | uuid | NO | - |
| assigned_by | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| id | uuid | NO | - |

### entity_tags

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| entity_id | uuid | NO | - |
| tag_id | uuid | NO | - |
| assigned_by | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| id | uuid | NO | - |

### entity_types

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| name | text | NO | - |
| description | text | YES | - |
| slug | text | NO | - |
| icon_url | text | YES | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |

### features

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| name | text | NO | - |
| slug | text | NO | - |
| description | text | YES | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| icon_url | text | YES | - |

### profile_activities

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| user_id | uuid | NO | - |
| type | USER-DEFINED | NO | - |
| description | text | NO | - |
| entity_id | uuid | YES | - |
| entity_name | text | YES | - |
| entity_slug | text | YES | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |

### review_votes

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| review_id | uuid | NO | - |
| user_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| id | uuid | NO | - |
| is_upvote | boolean | NO | - |

### reviews

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| entity_id | uuid | NO | - |
| user_id | uuid | NO | - |
| rating | integer | NO | - |
| title | text | YES | - |
| status | USER-DEFINED | NO | 'PENDING'::"ReviewStatus" |
| moderation_notes | text | YES | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| content | text | YES | - |
| downvotes | integer | NO | 0 |
| moderator_id | uuid | YES | - |
| upvotes | integer | NO | 0 |

### tags

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| name | text | NO | - |
| description | text | YES | - |
| slug | text | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |

### tool_requests

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| user_id | uuid | NO | - |
| tool_name | text | NO | - |
| description | text | NO | - |
| reason | text | NO | - |
| category_suggestion | text | YES | - |
| website_url | text | YES | - |
| priority | USER-DEFINED | NO | 'MEDIUM'::"ToolRequestPriority" |
| status | USER-DEFINED | NO | 'PENDING'::"ToolRequestStatus" |
| admin_notes | text | YES | - |
| votes | integer | NO | 0 |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |

### user_activity_logs

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| user_id | uuid | NO | - |
| action_type | USER-DEFINED | NO | - |
| entity_id | uuid | YES | - |
| category_id | uuid | YES | - |
| tag_id | uuid | YES | - |
| review_id | uuid | YES | - |
| target_user_id | uuid | YES | - |
| details | jsonb | YES | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |

### user_badges

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| user_id | uuid | NO | - |
| badge_type_id | uuid | NO | - |
| granted_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| notes | text | YES | - |
| granted_by | uuid | YES | - |

### user_followed_categories

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| user_id | uuid | NO | - |
| category_id | uuid | NO | - |
| followed_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |

### user_followed_tags

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| user_id | uuid | NO | - |
| tag_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| id | uuid | NO | - |

### user_notification_settings

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| user_id | uuid | NO | - |
| email_newsletter | boolean | NO | true |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| email_marketing | boolean | NO | true |
| email_on_new_entity_in_followed | boolean | NO | true |
| email_on_new_follower | boolean | NO | true |
| email_on_new_review | boolean | NO | true |
| email_on_review_response | boolean | NO | true |
| id | uuid | NO | - |

### user_preferences

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| user_id | uuid | NO | - |
| email_notifications | boolean | NO | true |
| marketing_emails | boolean | NO | false |
| weekly_digest | boolean | NO | true |
| new_tool_alerts | boolean | NO | true |
| profile_visibility | USER-DEFINED | NO | 'PUBLIC'::"ProfileVisibility" |
| show_bookmarks | boolean | NO | true |
| show_reviews | boolean | NO | true |
| show_activity | boolean | NO | true |
| theme | USER-DEFINED | NO | 'LIGHT'::"Theme" |
| items_per_page | integer | NO | 20 |
| default_view | USER-DEFINED | NO | 'GRID'::"DefaultView" |
| preferred_categories | ARRAY | YES | - |
| blocked_categories | ARRAY | YES | - |
| content_language | text | NO | 'en'::text |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |

### user_saved_entities

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| user_id | uuid | NO | - |
| entity_id | uuid | NO | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| id | uuid | NO | - |

### user_submitted_tools

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| user_id | uuid | NO | - |
| entity_id | uuid | NO | - |
| submission_status | USER-DEFINED | NO | 'PENDING'::"SubmissionStatus" |
| submitted_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| reviewed_at | timestamp without time zone | YES | - |
| reviewer_id | uuid | YES | - |
| reviewer_notes | text | YES | - |
| changes_requested | text | YES | - |

### users

| Column | Type | Nullable | Default |
|--------|------|----------|----------|
| id | uuid | NO | - |
| instance_id | uuid | YES | - |
| auth_user_id | uuid | NO | - |
| id | uuid | NO | - |
| aud | character varying | YES | - |
| username | text | YES | - |
| display_name | text | YES | - |
| role | character varying | YES | - |
| email | character varying | YES | - |
| email | text | NO | - |
| role | USER-DEFINED | NO | 'USER'::"UserRole" |
| encrypted_password | character varying | YES | - |
| email_confirmed_at | timestamp with time zone | YES | - |
| status | USER-DEFINED | NO | 'ACTIVE'::"UserStatus" |
| invited_at | timestamp with time zone | YES | - |
| technical_level | USER-DEFINED | YES | - |
| profile_picture_url | text | YES | - |
| confirmation_token | character varying | YES | - |
| confirmation_sent_at | timestamp with time zone | YES | - |
| bio | text | YES | - |
| social_links | jsonb | YES | - |
| recovery_token | character varying | YES | - |
| recovery_sent_at | timestamp with time zone | YES | - |
| created_at | timestamp without time zone | NO | CURRENT_TIMESTAMP |
| updated_at | timestamp without time zone | NO | - |
| email_change_token_new | character varying | YES | - |
| last_login | timestamp without time zone | YES | - |
| email_change | character varying | YES | - |
| email_change_sent_at | timestamp with time zone | YES | - |
| bookmarks_count | integer | NO | 0 |
| last_sign_in_at | timestamp with time zone | YES | - |
| reputation_score | integer | NO | 0 |
| requests_fulfilled | integer | NO | 0 |
| raw_app_meta_data | jsonb | YES | - |
| requests_made | integer | NO | 0 |
| raw_user_meta_data | jsonb | YES | - |
| reviews_count | integer | NO | 0 |
| is_super_admin | boolean | YES | - |
| tools_approved | integer | NO | 0 |
| created_at | timestamp with time zone | YES | - |
| updated_at | timestamp with time zone | YES | - |
| tools_submitted | integer | NO | 0 |
| phone | text | YES | NULL::character varying |
| phone_confirmed_at | timestamp with time zone | YES | - |
| phone_change | text | YES | ''::character varying |
| phone_change_token | character varying | YES | ''::character varying |
| phone_change_sent_at | timestamp with time zone | YES | - |
| confirmed_at | timestamp with time zone | YES | - |
| email_change_token_current | character varying | YES | ''::character varying |
| email_change_confirm_status | smallint | YES | 0 |
| banned_until | timestamp with time zone | YES | - |
| reauthentication_token | character varying | YES | ''::character varying |
| reauthentication_sent_at | timestamp with time zone | YES | - |
| is_sso_user | boolean | NO | false |
| deleted_at | timestamp with time zone | YES | - |
| is_anonymous | boolean | NO | false |

