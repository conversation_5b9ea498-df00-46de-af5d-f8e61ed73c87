# 🚀 Entity Filtering API - Quick Reference

## Base Endpoint
```
GET /entities
```

## 📊 Core Filters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `rating_min` | number | Minimum rating (1-5) | `4.0` |
| `rating_max` | number | Maximum rating (1-5) | `5.0` |
| `review_count_min` | number | Min review count | `10` |
| `review_count_max` | number | Max review count | `1000` |
| `affiliate_status` | string | Affiliate status | `APPROVED` |
| `has_affiliate_link` | boolean | Has affiliate link | `true` |

## 🔄 Sorting Options

| Sort By | Description |
|---------|-------------|
| `createdAt` | Creation date (default) |
| `updatedAt` | Last updated |
| `name` | Alphabetical |
| `foundedYear` | Company founding year |
| `averageRating` | ⭐ User ratings |
| `reviewCount` | 💬 Review popularity |
| `saveCount` | 💾 User saves |
| `popularity` | 🔥 Combined popularity |
| `relevance` | 🎯 Search relevance |

## 🎯 Entity Type Filters

### Tool Filters
```javascript
entity_type_filters: {
  tool: {
    // Quick filters
    has_free_tier: true,
    has_api: true,
    open_source: true,
    mobile_support: true,
    
    // Technical
    technical_levels: ['BEGINNER', 'INTERMEDIATE'],
    pricing_models: ['FREE', 'FREEMIUM'],
    platforms: ['Web', 'Mobile'],
    integrations: ['Slack', 'Discord']
  }
}
```

### Course Filters
```javascript
entity_type_filters: {
  course: {
    skill_levels: ['BEGINNER'],
    certificate_available: true,
    price_min: 0,
    price_max: 100,
    duration_weeks_min: 4,
    languages: ['English']
  }
}
```

### Job Filters
```javascript
entity_type_filters: {
  job: {
    employment_types: ['Full-time'],
    location_types: ['Remote'],
    salary_min: 80,
    salary_max: 150,
    visa_sponsorship: true,
    required_skills: ['Python', 'ML']
  }
}
```

## 🔗 Common Combinations

### High-Quality Free Tools
```javascript
{
  rating_min: 4.0,
  review_count_min: 10,
  entity_type_filters: {
    tool: {
      has_free_tier: true,
      technical_levels: ['BEGINNER', 'INTERMEDIATE']
    }
  },
  sortBy: 'averageRating',
  sortOrder: 'desc'
}
```

### Popular Remote Jobs
```javascript
{
  sortBy: 'popularity',
  sortOrder: 'desc',
  entity_type_filters: {
    job: {
      location_types: ['Remote'],
      salary_min: 100,
      employment_types: ['Full-time']
    }
  }
}
```

### Recent AI Research
```javascript
{
  entity_type_filters: {
    research_paper: {
      publication_date_from: '2023-01-01',
      research_areas: ['Machine Learning'],
      citation_count_min: 50
    }
  },
  sortBy: 'createdAt',
  sortOrder: 'desc'
}
```

## 📱 Frontend Implementation

### React Hook
```javascript
const useEntityFilters = () => {
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });

  const updateFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  return { filters, updateFilters };
};
```

### URL Building
```javascript
const buildUrl = (filters) => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (key === 'entity_type_filters') {
      params.set(key, JSON.stringify(value));
    } else if (Array.isArray(value)) {
      value.forEach(v => params.append(key, v));
    } else if (value !== undefined) {
      params.set(key, value);
    }
  });

  return `/entities?${params}`;
};
```

## 🎯 Pro Tips

### 1. **Performance**
- Use `limit` parameter for pagination
- Implement debouncing for search inputs
- Cache results when possible

### 2. **User Experience**
- Show result counts in real-time
- Provide filter presets for common searches
- Save user's filter preferences

### 3. **Error Handling**
```javascript
try {
  const response = await fetch(url);
  if (!response.ok) throw new Error(`HTTP ${response.status}`);
  return await response.json();
} catch (error) {
  console.error('Filter request failed:', error);
  // Show user-friendly message
}
```

### 4. **Analytics**
```javascript
// Track popular filter combinations
analytics.track('entity_filters_applied', {
  filter_count: Object.keys(filters).length,
  sort_by: filters.sortBy,
  has_entity_type_filters: !!filters.entity_type_filters
});
```

## 🌟 Advanced Features

### Smart Suggestions
```javascript
// Suggest filters based on current results
const suggestFilters = (currentResults) => {
  const suggestions = [];
  
  if (currentResults.length > 100) {
    suggestions.push({ rating_min: 4.0, label: 'Show only highly rated' });
  }
  
  if (currentResults.some(r => r.entityDetailsTool?.hasFreeTier)) {
    suggestions.push({ 
      entity_type_filters: { tool: { has_free_tier: true } },
      label: 'Free tools only'
    });
  }
  
  return suggestions;
};
```

### Filter Persistence
```javascript
// Save to localStorage
const saveFilters = (filters) => {
  localStorage.setItem('entityFilters', JSON.stringify(filters));
};

// Load from localStorage
const loadFilters = () => {
  const saved = localStorage.getItem('entityFilters');
  return saved ? JSON.parse(saved) : {};
};
```

## 📞 Quick Help

- **API Docs**: `/api-docs#/Entities/`
- **All Parameters**: Check OpenAPI spec for complete list
- **Response Format**: Includes `data`, `total`, `pagination`
- **Rate Limits**: Standard API rate limits apply

## 🎉 You're Ready!

Your filtering system now supports:
- ✅ 50+ filter parameters
- ✅ 9 entity type specific filter sets  
- ✅ 10+ advanced sorting options
- ✅ World-class AI entity discovery

**Happy filtering! 🚀**
