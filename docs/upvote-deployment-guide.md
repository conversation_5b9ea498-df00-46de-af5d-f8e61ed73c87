# Upvote Functionality Deployment Guide

## 🚀 Quick Deployment Steps

### 1. Apply Database Migration

The upvote functionality requires database schema changes. Apply the migration:

```bash
# Option A: Using Prisma (recommended)
npx prisma migrate deploy

# Option B: Manual SQL execution (if <PERSON><PERSON><PERSON> fails)
# Execute the SQL from: prisma/migrations/20250622062139_create_upvote_count_trigger/migration.sql
```

### 2. Verify Database Schema

Run the monitoring queries to verify the schema:

```bash
# Execute the queries in scripts/upvote-performance-monitoring.sql
# Or use the Node.js performance test:
node scripts/test-upvote-performance.js
```

### 3. Start the Application

```bash
npm run start:dev
# or for production:
npm run start:prod
```

### 4. Verify API Endpoints

The following endpoints should be available:

- **POST** `/entities/:entityId/upvote` - Add upvote
- **DELETE** `/entities/:entityId/upvote` - Remove upvote

Check Swagger documentation at: `http://localhost:3000/api-docs`

## 🔧 Database Schema Changes

### New Table: `user_upvotes`
```sql
CREATE TABLE "public"."user_upvotes" (
    "user_id" UUID NOT NULL,
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "user_upvotes_pkey" PRIMARY KEY ("user_id","entity_id")
);
```

### Modified Table: `entities`
```sql
ALTER TABLE "public"."entities" 
ADD COLUMN "upvote_count" INTEGER NOT NULL DEFAULT 0;
```

### Database Trigger
```sql
CREATE OR REPLACE FUNCTION public.update_entity_upvote_count()
RETURNS TRIGGER AS $$
BEGIN
  IF (TG_OP = 'DELETE') THEN
    UPDATE public.entities
    SET upvote_count = upvote_count - 1
    WHERE id = OLD.entity_id;
    RETURN OLD;
  ELSIF (TG_OP = 'INSERT') THEN
    UPDATE public.entities
    SET upvote_count = upvote_count + 1
    WHERE id = NEW.entity_id;
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER trigger_update_entity_upvote_count
AFTER INSERT OR DELETE ON public.user_upvotes
FOR EACH ROW EXECUTE FUNCTION public.update_entity_upvote_count();
```

## 🧪 Testing

### Unit Tests
```bash
# Run upvote-specific tests
npm test -- --testPathPattern=upvotes

# Run controller tests
npm test -- src/upvotes/upvotes.controller.spec.ts

# Run service tests  
npm test -- src/upvotes/upvotes.service.spec.ts
```

### Integration Tests
```bash
# Run integration tests (requires database connection)
npm test -- src/upvotes/upvotes.integration.spec.ts
```

### Performance Tests
```bash
# Run performance monitoring script
node scripts/test-upvote-performance.js
```

## 📊 Monitoring

### Performance Queries
Execute queries from `scripts/upvote-performance-monitoring.sql` to monitor:

- Upvote count accuracy
- Trigger performance
- User activity
- Data consistency

### Key Metrics to Monitor
- **Response Time**: Upvote operations should complete in <100ms
- **Data Consistency**: `upvote_count` should match actual upvote records
- **Error Rate**: Should be <1% (mostly authentication errors)
- **Trigger Performance**: Should not add significant overhead

## 🔒 Security Features

- **JWT Authentication**: All endpoints require valid authentication
- **Input Validation**: UUID validation prevents injection attacks
- **User Isolation**: Users can only manage their own upvotes
- **Rate Limiting**: Inherited from global throttling configuration

## 🚨 Troubleshooting

### Common Issues

1. **Migration Fails**
   - Check database connectivity
   - Verify Prisma configuration
   - Apply SQL manually if needed

2. **Endpoints Return 404**
   - Verify UpvotesModule is imported in AppModule
   - Check application startup logs
   - Confirm routes are registered

3. **Trigger Not Working**
   - Verify trigger function exists
   - Check trigger is attached to table
   - Test with manual SQL operations

4. **Performance Issues**
   - Monitor trigger execution time
   - Check for missing indexes
   - Verify database connection pool

### Debug Commands

```bash
# Check application status
curl http://localhost:3000/health

# Test endpoint accessibility (should return 401)
curl -X POST http://localhost:3000/entities/test-uuid/upvote

# Check Swagger documentation
curl http://localhost:3000/api-docs
```

## 📈 Performance Expectations

- **Upvote Creation**: <50ms average
- **Upvote Removal**: <50ms average  
- **Count Updates**: Automatic via trigger
- **Concurrent Users**: Supports high concurrency
- **Data Consistency**: 100% accurate with triggers

## 🔄 Rollback Plan

If issues occur, rollback steps:

1. **Remove UpvotesModule** from AppModule imports
2. **Revert Prisma schema** changes
3. **Drop database objects**:
   ```sql
   DROP TRIGGER IF EXISTS trigger_update_entity_upvote_count ON public.user_upvotes;
   DROP FUNCTION IF EXISTS public.update_entity_upvote_count();
   DROP TABLE IF EXISTS public.user_upvotes;
   ALTER TABLE public.entities DROP COLUMN IF EXISTS upvote_count;
   ```

## ✅ Deployment Checklist

- [ ] Database migration applied successfully
- [ ] Trigger function created and working
- [ ] Application starts without errors
- [ ] Swagger documentation shows upvote endpoints
- [ ] Unit tests pass
- [ ] Performance tests pass
- [ ] Endpoints return correct HTTP status codes
- [ ] Authentication is working
- [ ] Data consistency verified

## 🎯 Next Steps

After successful deployment:

1. **Monitor Performance**: Use the monitoring queries regularly
2. **Frontend Integration**: Implement upvote UI components
3. **Analytics**: Track upvote trends and user engagement
4. **Optimization**: Consider caching for high-traffic entities
5. **Features**: Plan additional upvote-related features
