import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsUrl } from 'class-validator';

/**
 * DTO for social links structure to match frontend expectations
 */
export class SocialLinksDto {
  @ApiPropertyOptional({ 
    description: 'Personal or company website URL',
    example: 'https://johndoe.com'
  })
  @IsOptional()
  @IsUrl({}, { message: 'Website must be a valid URL' })
  website?: string;

  @ApiPropertyOptional({ 
    description: 'Twitter profile URL',
    example: 'https://twitter.com/johndoe'
  })
  @IsOptional()
  @IsUrl({}, { message: 'Twitter must be a valid URL' })
  twitter?: string;

  @ApiPropertyOptional({ 
    description: 'LinkedIn profile URL',
    example: 'https://linkedin.com/in/johndoe'
  })
  @IsOptional()
  @IsUrl({}, { message: 'LinkedIn must be a valid URL' })
  linkedin?: string;

  @ApiPropertyOptional({ 
    description: 'GitHub profile URL',
    example: 'https://github.com/johndoe'
  })
  @IsOptional()
  @IsUrl({}, { message: 'GitHub must be a valid URL' })
  github?: string;
}
