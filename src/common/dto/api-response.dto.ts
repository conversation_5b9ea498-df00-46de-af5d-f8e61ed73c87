import { ApiProperty } from '@nestjs/swagger';

/**
 * Standard API response wrapper that provides consistent response format
 * across all endpoints with success indicator and data payload
 */
export class ApiResponseDto<T> {
  @ApiProperty({ 
    description: 'Indicates if the request was successful',
    example: true 
  })
  success: boolean;

  @ApiProperty({
    description: 'The response data payload'
  })
  data: T;

  constructor(data: T, success: boolean = true) {
    this.success = success;
    this.data = data;
  }

  /**
   * Static factory method to create a successful response
   */
  static success<T>(data: T): ApiResponseDto<T> {
    return new ApiResponseDto(data, true);
  }

  /**
   * Static factory method to create an error response
   */
  static error<T>(data: T): ApiResponseDto<T> {
    return new ApiResponseDto(data, false);
  }
}

/**
 * Specific wrapper for the comprehensive profile response
 * to ensure proper typing and documentation
 */
export class ComprehensiveProfileApiResponseDto {
  @ApiProperty({ 
    description: 'Indicates if the request was successful',
    example: true 
  })
  success: boolean;

  @ApiProperty({
    description: 'The comprehensive profile data'
  })
  data: {
    user: any;
    preferences: any;
    stats: any;
    recent_activity: any[];
  };
}
