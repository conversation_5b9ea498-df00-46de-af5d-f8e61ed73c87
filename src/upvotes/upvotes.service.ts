import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma, UserUpvote } from 'generated/prisma';
import { AppLoggerService } from '../common/logger/logger.service';

@Injectable()
export class UpvotesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logger: AppLoggerService,
  ) {}

  async addUpvote(userId: string, entityId: string): Promise<UserUpvote> {
    // 1. Verify the entity exists
    const entity = await this.prisma.entity.findUnique({
      where: { id: entityId },
      select: { id: true, name: true },
    });

    if (!entity) {
      throw new NotFoundException(`Entity with ID "${entityId}" not found.`);
    }

    // 2. Use create to add the upvote
    // The compound primary key ensures a user can only upvote an entity once
    try {
      const upvote = await this.prisma.userUpvote.create({
        data: {
          userId: userId,
          entityId: entityId,
        },
      });

      this.logger.log('Upvote successfully added', {
        operation: 'addUpvote',
        userId,
        entityId,
      });

      return upvote;
    } catch (error) {
      // Check for specific Prisma errors and provide better error messages
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            // Unique constraint violation - user already upvoted this entity
            this.logger.log('User already upvoted this entity - treating as success', {
              operation: 'addUpvote',
              userId,
              entityId,
              reason: 'already_upvoted',
            });
            // Return the existing upvote instead of throwing an error (idempotent)
            const existingUpvote = await this.prisma.userUpvote.findUnique({
              where: {
                userId_entityId: {
                  userId: userId,
                  entityId: entityId,
                },
              },
            });
            if (existingUpvote) {
              return existingUpvote;
            }
            break;
          case 'P2003':
            // Foreign key constraint failed - likely invalid userId or entityId
            throw new NotFoundException(`Invalid user ID or entity ID provided.`);
          default:
            this.logger.logError(error, {
              operation: 'addUpvote',
              userId,
              entityId,
              errorCode: error.code,
              type: 'prisma_unknown_error',
            });
            break;
        }
      }

      throw new InternalServerErrorException('Could not add the upvote. Please try again later.');
    }
  }

  async removeUpvote(userId: string, entityId: string): Promise<void> {
    try {
      await this.prisma.userUpvote.delete({
        where: {
          userId_entityId: {
            userId: userId,
            entityId: entityId,
          },
        },
      });

      this.logger.log('Upvote successfully removed', {
        operation: 'removeUpvote',
        userId,
        entityId,
      });

      // If delete is successful, no return value is needed for a 204 No Content response.
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // P2025: "An operation failed because it depends on one or more records that were required but not found."
        // This means the upvote to delete didn't exist.
        // For a DELETE operation, this is often treated as idempotent success (already deleted).
        if (error.code === 'P2025') {
          // Log that the upvote was not found, but don't throw an error.
          this.logger.log('Upvote not found during delete - treating as success', {
            operation: 'removeUpvote',
            userId,
            entityId,
            reason: 'upvote_not_found',
          });
          return; // Still a success from the client's perspective (it's not upvoted)
        }
      }

      this.logger.logError(error as Error, {
        operation: 'removeUpvote',
        userId,
        entityId,
        type: 'upvote_delete_error',
      });

      throw new InternalServerErrorException('Could not remove the upvote. Please try again later.');
    }
  }
}
