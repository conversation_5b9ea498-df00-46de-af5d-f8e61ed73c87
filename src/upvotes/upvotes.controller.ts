import { Controller, Post, Delete, Param, UseGuards, HttpCode, HttpStatus, ParseUUIDPipe, BadRequestException } from '@nestjs/common';
import { UpvotesService } from './upvotes.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { User as UserModel } from 'generated/prisma'; // For req.user typing
import { AppLoggerService } from '../common/logger/logger.service';

@ApiTags('Upvotes')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('entities/:entityId/upvote')
export class UpvotesController {
  constructor(
    private readonly upvotesService: UpvotesService,
    private readonly logger: AppLoggerService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Upvote an entity' })
  @ApiParam({ name: 'entityId', required: true, description: 'UUID of the entity to upvote', type: String, format: 'uuid' })
  @ApiResponse({ status: 201, description: 'Entity upvoted successfully.' })
  @ApiResponse({ status: 200, description: 'Entity was already upvoted (idempotent operation).' })
  @ApiResponse({ status: 400, description: 'Invalid input or UUID format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Entity not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async addUpvote(
    @GetUser() user: UserModel,
    @Param('entityId', ParseUUIDPipe) entityId: string
  ) {
    // Validate user ID
    if (!user?.id) {
      this.logger.logError(new Error('User ID missing in upvote request'), {
        operation: 'addUpvote',
        entityId,
        type: 'missing_user_id',
      });
      throw new BadRequestException('User authentication failed. Please log in again.');
    }

    this.logger.log('Upvote request received', {
      operation: 'addUpvote',
      userId: user.id,
      entityId,
    });

    return this.upvotesService.addUpvote(user.id, entityId);
  }

  @Delete()
  @ApiOperation({ summary: 'Remove upvote from an entity' })
  @ApiParam({ name: 'entityId', required: true, description: 'UUID of the entity to remove upvote from', type: String, format: 'uuid' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiResponse({ status: 204, description: 'Upvote removed successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid UUID format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async removeUpvote(
    @GetUser() user: UserModel,
    @Param('entityId', ParseUUIDPipe) entityId: string
  ) {
    // Validate user ID
    if (!user?.id) {
      this.logger.logError(new Error('User ID missing in upvote delete request'), {
        operation: 'removeUpvote',
        entityId,
        type: 'missing_user_id',
      });
      throw new BadRequestException('User authentication failed. Please log in again.');
    }

    this.logger.log('Remove upvote request received', {
      operation: 'removeUpvote',
      userId: user.id,
      entityId,
    });

    return this.upvotesService.removeUpvote(user.id, entityId);
  }
}
