import { Modu<PERSON> } from '@nestjs/common';
import { UpvotesService } from './upvotes.service';
import { UpvotesController } from './upvotes.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module'; // For JwtAuthGuard dependency
import { LoggerModule } from '../common/logger/logger.module';

@Module({
  imports: [
    PrismaModule,
    AuthModule, // To make JwtAuthGuard available and req.user populated
    LoggerModule, // For structured logging
  ],
  controllers: [UpvotesController],
  providers: [UpvotesService],
})
export class UpvotesModule {}
