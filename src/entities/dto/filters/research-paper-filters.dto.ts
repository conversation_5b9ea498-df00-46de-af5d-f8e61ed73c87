import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsDateString, IsInt, Min, Max } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Research Paper-specific filters for enhanced AI research discovery
 */
export class ResearchPaperFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by authors (searches within the JSON array)',
    type: [String],
    example: ['<PERSON>', '<PERSON><PERSON>', 'Yoshu<PERSON>'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  authors?: string[];

  @ApiPropertyOptional({
    description: 'Filter by research areas (searches within the JSON array)',
    type: [String],
    example: ['Machine Learning', 'Natural Language Processing', 'Computer Vision', 'Robotics'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  research_areas?: string[];

  @ApiPropertyOptional({
    description: 'Filter by publication venues (searches within the JSON array)',
    type: [String],
    example: ['NeurIPS', 'ICML', 'ICLR', 'AAAI', 'Nature'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  publication_venues?: string[];

  @ApiPropertyOptional({
    description: 'Filter by keywords (searches within the JSON array)',
    type: [String],
    example: ['transformer', 'attention', 'neural network', 'deep learning'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  keywords?: string[];

  @ApiPropertyOptional({
    description: 'Search by DOI (partial match)',
    example: '10.1038',
  })
  @IsOptional()
  @IsString()
  doi?: string;

  @ApiPropertyOptional({
    description: 'Filter by publication date from (YYYY-MM-DD)',
    example: '2020-01-01',
  })
  @IsOptional()
  @IsDateString()
  publication_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter by publication date to (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  publication_date_to?: string;

  @ApiPropertyOptional({
    description: 'Minimum citation count',
    type: Number,
    minimum: 0,
    example: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  citation_count_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum citation count',
    type: Number,
    minimum: 0,
    example: 10000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  citation_count_max?: number;

  @ApiPropertyOptional({
    description: 'Search in abstract (partial match)',
    example: 'attention mechanism',
  })
  @IsOptional()
  @IsString()
  abstract_search?: string;

  @ApiPropertyOptional({
    description: 'Search in authors (searches within the JSON array)',
    example: 'hinton',
  })
  @IsOptional()
  @IsString()
  authors_search?: string;

  @ApiPropertyOptional({
    description: 'Search in research areas (searches within the JSON array)',
    example: 'nlp',
  })
  @IsOptional()
  @IsString()
  research_areas_search?: string;

  @ApiPropertyOptional({
    description: 'Search in publication venues (searches within the JSON array)',
    example: 'neurips',
  })
  @IsOptional()
  @IsString()
  venues_search?: string;

  @ApiPropertyOptional({
    description: 'Search in keywords (searches within the JSON array)',
    example: 'transformer',
  })
  @IsOptional()
  @IsString()
  keywords_search?: string;
}
