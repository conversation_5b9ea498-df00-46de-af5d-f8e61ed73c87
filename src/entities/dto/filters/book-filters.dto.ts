import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsBoolean, IsDateString, IsInt, Min, Max } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Book-specific filters for enhanced AI book discovery
 */
export class BookFiltersDto {
  @ApiPropertyOptional({
    description: 'Search by author name (partial match)',
    example: 'Stuart Russell',
  })
  @IsOptional()
  @IsString()
  author?: string;

  @ApiPropertyOptional({
    description: 'Search by publisher (partial match)',
    example: 'MIT Press',
  })
  @IsOptional()
  @IsString()
  publisher?: string;

  @ApiPropertyOptional({
    description: 'Search by ISBN (partial match)',
    example: '978-0262039',
  })
  @IsOptional()
  @IsString()
  isbn?: string;

  @ApiPropertyOptional({
    description: 'Filter by book format',
    type: [String],
    example: ['Hardcover', 'Paperback', 'eBook', 'Audiobook'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  formats?: string[];

  @ApiPropertyOptional({
    description: 'Filter by publication date from (YYYY-MM-DD)',
    example: '2020-01-01',
  })
  @IsOptional()
  @IsDateString()
  publication_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter by publication date to (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  publication_date_to?: string;

  @ApiPropertyOptional({
    description: 'Minimum page count',
    type: Number,
    minimum: 1,
    example: 200,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page_count_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum page count',
    type: Number,
    minimum: 1,
    example: 1000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page_count_max?: number;

  @ApiPropertyOptional({
    description: 'Filter books that have purchase URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_purchase_url?: boolean;

  @ApiPropertyOptional({
    description: 'Search in book summary (partial match)',
    example: 'artificial intelligence',
  })
  @IsOptional()
  @IsString()
  summary_search?: string;

  @ApiPropertyOptional({
    description: 'Search by format (partial match)',
    example: 'ebook',
  })
  @IsOptional()
  @IsString()
  format_search?: string;
}
