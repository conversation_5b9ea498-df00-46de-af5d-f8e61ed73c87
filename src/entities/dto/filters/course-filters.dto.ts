import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsEnum, IsBoolean, IsString, IsInt, Min, Max } from 'class-validator';
import { SkillLevel } from '@generated-prisma';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Course-specific filters for enhanced search
 */
export class CourseFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by skill levels required for the course',
    enum: SkillLevel,
    isArray: true,
    example: [SkillLevel.BEGINNER, SkillLevel.INTERMEDIATE],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(SkillLevel, { each: true })
  skill_levels?: SkillLevel[];

  @ApiPropertyOptional({
    description: 'Filter by whether a certificate is available',
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  certificate_available?: boolean;

  @ApiPropertyOptional({
    description: 'Search by instructor name (partial match)',
    example: 'Dr. Smith',
  })
  @IsOptional()
  @IsString()
  instructor_name?: string;

  @ApiPropertyOptional({
    description: 'Search by course duration text (partial match)',
    example: '10 hours',
  })
  @IsOptional()
  @IsString()
  duration_text?: string;

  @ApiPropertyOptional({
    description: 'Minimum enrollment count',
    type: Number,
    minimum: 0,
    example: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  enrollment_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum enrollment count',
    type: Number,
    minimum: 0,
    example: 10000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  enrollment_max?: number;

  @ApiPropertyOptional({
    description: 'Search in course prerequisites (partial match)',
    example: 'programming',
  })
  @IsOptional()
  @IsString()
  prerequisites?: string;

  @ApiPropertyOptional({
    description: 'Filter courses that have a syllabus URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_syllabus?: boolean;
}
