import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsBoolean } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Agency-specific filters for enhanced AI agency discovery
 */
export class AgencyFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by services offered (searches within the JSON array)',
    type: [String],
    example: ['AI Strategy', 'Machine Learning', 'Data Science', 'Automation'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  services_offered?: string[];

  @ApiPropertyOptional({
    description: 'Filter by industry focus (searches within the JSON array)',
    type: [String],
    example: ['Healthcare', 'Finance', 'E-commerce', 'Manufacturing'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  industry_focus?: string[];

  @ApiPropertyOptional({
    description: 'Filter by target client size (searches within the JSON array)',
    type: [String],
    example: ['Startup', 'SMB', 'Enterprise', 'Fortune 500'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  target_client_size?: string[];

  @ApiPropertyOptional({
    description: 'Filter by target audience (searches within the JSON array)',
    type: [String],
    example: ['CTOs', 'Data Scientists', 'Business Leaders', 'Developers'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  target_audience?: string[];

  @ApiPropertyOptional({
    description: 'Search by location summary (partial match)',
    example: 'San Francisco',
  })
  @IsOptional()
  @IsString()
  location_summary?: string;

  @ApiPropertyOptional({
    description: 'Filter agencies that have portfolio URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_portfolio?: boolean;

  @ApiPropertyOptional({
    description: 'Search in pricing information (partial match)',
    example: 'hourly rate',
  })
  @IsOptional()
  @IsString()
  pricing_info_search?: string;

  @ApiPropertyOptional({
    description: 'Search in services offered (searches within the JSON array)',
    example: 'machine learning',
  })
  @IsOptional()
  @IsString()
  services_search?: string;

  @ApiPropertyOptional({
    description: 'Search in industry focus (searches within the JSON array)',
    example: 'healthcare',
  })
  @IsOptional()
  @IsString()
  industry_search?: string;

  @ApiPropertyOptional({
    description: 'Search in target audience (searches within the JSON array)',
    example: 'developers',
  })
  @IsOptional()
  @IsString()
  audience_search?: string;
}
