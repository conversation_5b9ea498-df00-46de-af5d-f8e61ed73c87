import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsEnum, IsBoolean, IsString, IsInt, Min, Max, IsDateString } from 'class-validator';
import { TechnicalLevel, LearningCurve, PricingModel, PriceRange } from '@generated-prisma';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Tool-specific filters for enhanced AI tool discovery
 */
export class ToolFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by technical level required to use the tool',
    enum: TechnicalLevel,
    isArray: true,
    example: [TechnicalLevel.BEGINNER, TechnicalLevel.INTERMEDIATE],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(TechnicalLevel, { each: true })
  technical_levels?: TechnicalLevel[];

  @ApiPropertyOptional({
    description: 'Filter by learning curve difficulty',
    enum: LearningCurve,
    isArray: true,
    example: [LearningCurve.LOW, LearningCurve.MEDIUM],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(LearningCurve, { each: true })
  learning_curves?: LearningCurve[];

  @ApiPropertyOptional({
    description: 'Filter by pricing model',
    enum: PricingModel,
    isArray: true,
    example: [PricingModel.FREE, PricingModel.FREEMIUM],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(PricingModel, { each: true })
  pricing_models?: PricingModel[];

  @ApiPropertyOptional({
    description: 'Filter by price range',
    enum: PriceRange,
    isArray: true,
    example: [PriceRange.FREE, PriceRange.LOW],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(PriceRange, { each: true })
  price_ranges?: PriceRange[];

  @ApiPropertyOptional({
    description: 'Filter tools that have API access',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_api?: boolean;

  @ApiPropertyOptional({
    description: 'Filter tools that have a free tier',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_free_tier?: boolean;

  @ApiPropertyOptional({
    description: 'Filter tools that are open source',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  open_source?: boolean;

  @ApiPropertyOptional({
    description: 'Filter tools that have mobile support',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  mobile_support?: boolean;

  @ApiPropertyOptional({
    description: 'Filter tools that have demo available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  demo_available?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by supported platforms',
    type: [String],
    example: ['Web', 'iOS', 'Android', 'Desktop'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  platforms?: string[];

  @ApiPropertyOptional({
    description: 'Filter by integrations (searches within the JSON array)',
    type: [String],
    example: ['Slack', 'Discord', 'Zapier'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  integrations?: string[];

  @ApiPropertyOptional({
    description: 'Filter by frameworks supported',
    type: [String],
    example: ['TensorFlow', 'PyTorch', 'Hugging Face'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  frameworks?: string[];

  @ApiPropertyOptional({
    description: 'Filter by libraries supported',
    type: [String],
    example: ['OpenAI', 'Anthropic', 'Cohere'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  libraries?: string[];

  @ApiPropertyOptional({
    description: 'Search in key features (searches within the JSON array)',
    example: 'natural language processing',
  })
  @IsOptional()
  @IsString()
  key_features_search?: string;

  @ApiPropertyOptional({
    description: 'Search in use cases (searches within the JSON array)',
    example: 'content generation',
  })
  @IsOptional()
  @IsString()
  use_cases_search?: string;

  @ApiPropertyOptional({
    description: 'Search in target audience (searches within the JSON array)',
    example: 'developers',
  })
  @IsOptional()
  @IsString()
  target_audience_search?: string;

  @ApiPropertyOptional({
    description: 'Filter by deployment options',
    type: [String],
    example: ['Cloud', 'On-premise', 'Hybrid'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  deployment_options?: string[];

  @ApiPropertyOptional({
    description: 'Filter by support channels available',
    type: [String],
    example: ['Email', 'Chat', 'Phone', 'Community'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  support_channels?: string[];

  @ApiPropertyOptional({
    description: 'Filter tools that have live chat support',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_live_chat?: boolean;

  @ApiPropertyOptional({
    description: 'Search by customization level',
    example: 'high',
  })
  @IsOptional()
  @IsString()
  customization_level?: string;

  @ApiPropertyOptional({
    description: 'Search in pricing details (partial match)',
    example: 'per user',
  })
  @IsOptional()
  @IsString()
  pricing_details_search?: string;
}
