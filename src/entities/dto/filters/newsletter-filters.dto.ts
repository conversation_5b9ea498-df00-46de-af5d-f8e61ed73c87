import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsBoolean, IsInt, Min } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Newsletter-specific filters for enhanced AI newsletter discovery
 */
export class NewsletterFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by newsletter frequency',
    type: [String],
    example: ['Daily', 'Weekly', 'Bi-weekly', 'Monthly'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  frequency?: string[];

  @ApiPropertyOptional({
    description: 'Filter by focus areas (searches within the JSON array)',
    type: [String],
    example: ['AI News', 'Research', 'Industry Updates', 'Tutorials'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  focus_areas?: string[];

  @ApiPropertyOptional({
    description: 'Filter by target audience (searches within the JSON array)',
    type: [String],
    example: ['Developers', 'Researchers', 'Business Leaders', 'Students'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  target_audience?: string[];

  @ApiPropertyOptional({
    description: 'Minimum subscriber count',
    type: Number,
    minimum: 0,
    example: 1000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  subscriber_count_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum subscriber count',
    type: Number,
    minimum: 0,
    example: 100000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  subscriber_count_max?: number;

  @ApiPropertyOptional({
    description: 'Filter newsletters that are free',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  is_free?: boolean;

  @ApiPropertyOptional({
    description: 'Filter newsletters that have archives available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_archives?: boolean;

  @ApiPropertyOptional({
    description: 'Search by author/creator (partial match)',
    example: 'Andrew Ng',
  })
  @IsOptional()
  @IsString()
  author?: string;

  @ApiPropertyOptional({
    description: 'Search in focus areas (searches within the JSON array)',
    example: 'machine learning',
  })
  @IsOptional()
  @IsString()
  focus_areas_search?: string;

  @ApiPropertyOptional({
    description: 'Search in target audience (searches within the JSON array)',
    example: 'developers',
  })
  @IsOptional()
  @IsString()
  audience_search?: string;

  @ApiPropertyOptional({
    description: 'Search by frequency (partial match)',
    example: 'weekly',
  })
  @IsOptional()
  @IsString()
  frequency_search?: string;
}
