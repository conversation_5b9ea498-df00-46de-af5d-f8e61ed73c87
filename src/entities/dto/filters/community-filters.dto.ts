import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsBoolean, IsInt, Min } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Community-specific filters for enhanced AI community discovery
 */
export class CommunityFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by community type',
    type: [String],
    example: ['Discord', 'Slack', 'Reddit', 'Forum', 'Telegram'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  community_types?: string[];

  @ApiPropertyOptional({
    description: 'Filter by focus areas (searches within the JSON array)',
    type: [String],
    example: ['Machine Learning', 'AI Research', 'Startups', 'Open Source'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  focus_areas?: string[];

  @ApiPropertyOptional({
    description: 'Filter by target audience (searches within the JSON array)',
    type: [String],
    example: ['Developers', 'Researchers', 'Students', 'Entrepreneurs'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  target_audience?: string[];

  @ApiPropertyOptional({
    description: 'Minimum member count',
    type: Number,
    minimum: 0,
    example: 1000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  member_count_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum member count',
    type: Number,
    minimum: 0,
    example: 100000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  member_count_max?: number;

  @ApiPropertyOptional({
    description: 'Filter communities that are free to join',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  is_free?: boolean;

  @ApiPropertyOptional({
    description: 'Filter communities that require invitation',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  requires_invitation?: boolean;

  @ApiPropertyOptional({
    description: 'Filter communities that have events',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_events?: boolean;

  @ApiPropertyOptional({
    description: 'Search in focus areas (searches within the JSON array)',
    example: 'machine learning',
  })
  @IsOptional()
  @IsString()
  focus_areas_search?: string;

  @ApiPropertyOptional({
    description: 'Search in target audience (searches within the JSON array)',
    example: 'developers',
  })
  @IsOptional()
  @IsString()
  audience_search?: string;

  @ApiPropertyOptional({
    description: 'Search by community type (partial match)',
    example: 'discord',
  })
  @IsOptional()
  @IsString()
  type_search?: string;
}
