import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsDateString, IsInt, Min, Max } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Hardware-specific filters for enhanced search
 */
export class HardwareFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by hardware types',
    type: [String],
    example: ['GPU', 'CPU', 'FPGA', 'TPU', 'ASIC'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  hardware_types?: string[];

  @ApiPropertyOptional({
    description: 'Filter by manufacturers',
    type: [String],
    example: ['NVIDIA', 'Intel', 'AMD', 'Apple'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  manufacturers?: string[];

  @ApiPropertyOptional({
    description: 'Filter by release date from (YYYY-MM-DD)',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDateString()
  release_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter by release date to (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  release_date_to?: string;

  @ApiPropertyOptional({
    description: 'Search in price range text (partial match)',
    example: '$500',
  })
  @IsOptional()
  @IsString()
  price_range?: string;

  @ApiPropertyOptional({
    description: 'Minimum price (in dollars)',
    type: Number,
    minimum: 0,
    example: 500,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  price_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum price (in dollars)',
    type: Number,
    minimum: 0,
    example: 2000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  price_max?: number;

  @ApiPropertyOptional({
    description: 'Search in specifications (searches within the JSON object)',
    example: 'GDDR6',
  })
  @IsOptional()
  @IsString()
  specifications_search?: string;

  @ApiPropertyOptional({
    description: 'Filter hardware that has datasheet URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  has_datasheet?: boolean;

  @ApiPropertyOptional({
    description: 'Search by memory specifications (partial match)',
    example: '16GB',
  })
  @IsOptional()
  @IsString()
  memory_search?: string;

  @ApiPropertyOptional({
    description: 'Search by processor specifications (partial match)',
    example: 'Intel i9',
  })
  @IsOptional()
  @IsString()
  processor_search?: string;
}
