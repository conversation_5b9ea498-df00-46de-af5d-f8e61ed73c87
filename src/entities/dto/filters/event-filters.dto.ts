import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsDateString, IsBoolean, IsInt, Min, Max } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Event-specific filters for enhanced search
 */
export class EventFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by event types',
    type: [String],
    example: ['Conference', 'Workshop', 'Webinar', 'Meetup', 'Hackathon'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  event_types?: string[];

  @ApiPropertyOptional({
    description: 'Filter events starting from this date (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  start_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter events starting before this date (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  start_date_to?: string;

  @ApiPropertyOptional({
    description: 'Filter events ending from this date (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  end_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter events ending before this date (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  end_date_to?: string;

  @ApiPropertyOptional({
    description: 'Filter by whether the event is online',
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  is_online?: boolean;

  @ApiPropertyOptional({
    description: 'Search by event location (partial match)',
    example: 'San Francisco',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Search in event price text (partial match)',
    example: 'Free',
  })
  @IsOptional()
  @IsString()
  price_text?: string;

  @ApiPropertyOptional({
    description: 'Filter events that require registration',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  registration_required?: boolean;

  @ApiPropertyOptional({
    description: 'Filter events that have registration URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_registration_url?: boolean;

  @ApiPropertyOptional({
    description: 'Search by key speakers (partial match)',
    example: 'Elon Musk',
  })
  @IsOptional()
  @IsString()
  speakers_search?: string;

  @ApiPropertyOptional({
    description: 'Search by target audience (partial match)',
    example: 'developers',
  })
  @IsOptional()
  @IsString()
  target_audience_search?: string;
}
