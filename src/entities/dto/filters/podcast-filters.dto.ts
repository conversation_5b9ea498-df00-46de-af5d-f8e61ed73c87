import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsBoolean } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Podcast-specific filters for enhanced AI podcast discovery
 */
export class PodcastFiltersDto {
  @ApiPropertyOptional({
    description: 'Search by host name (partial match)',
    example: '<PERSON>',
  })
  @IsOptional()
  @IsString()
  host?: string;

  @ApiPropertyOptional({
    description: 'Filter by main topics (searches within the array)',
    type: [String],
    example: ['AI', 'Machine Learning', 'Deep Learning', 'Robotics'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  main_topics?: string[];

  @ApiPropertyOptional({
    description: 'Filter by frequency',
    type: [String],
    example: ['Weekly', 'Bi-weekly', 'Monthly', 'Daily'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  frequency?: string[];

  @ApiPropertyOptional({
    description: 'Search by average episode length (partial match)',
    example: '60 minutes',
  })
  @IsOptional()
  @IsString()
  average_length?: string;

  @ApiPropertyOptional({
    description: 'Filter podcasts available on Spotify',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_spotify?: boolean;

  @ApiPropertyOptional({
    description: 'Filter podcasts available on Apple Podcasts',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_apple_podcasts?: boolean;

  @ApiPropertyOptional({
    description: 'Filter podcasts available on Google Podcasts',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_google_podcasts?: boolean;

  @ApiPropertyOptional({
    description: 'Filter podcasts available on YouTube',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_youtube?: boolean;

  @ApiPropertyOptional({
    description: 'Search in main topics (searches within the array)',
    example: 'machine learning',
  })
  @IsOptional()
  @IsString()
  topics_search?: string;

  @ApiPropertyOptional({
    description: 'Search by frequency (partial match)',
    example: 'weekly',
  })
  @IsOptional()
  @IsString()
  frequency_search?: string;
}
