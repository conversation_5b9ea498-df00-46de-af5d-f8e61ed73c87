import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsBoolean, IsDateString } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Software-specific filters for enhanced AI software discovery
 */
export class SoftwareFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by license type',
    type: [String],
    example: ['MIT', 'Apache 2.0', 'GPL', 'Commercial', 'Proprietary'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  license_types?: string[];

  @ApiPropertyOptional({
    description: 'Filter by programming languages (searches within the JSON array)',
    type: [String],
    example: ['Python', 'JavaScript', 'Java', 'C++', 'R'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  programming_languages?: string[];

  @ApiPropertyOptional({
    description: 'Filter by platform compatibility (searches within the JSON array)',
    type: [String],
    example: ['Windows', 'macOS', 'Linux', 'Web', 'Mobile'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  platform_compatibility?: string[];

  @ApiPropertyOptional({
    description: 'Filter software that is open source',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  open_source?: boolean;

  @ApiPropertyOptional({
    description: 'Filter software that has repository URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_repository?: boolean;

  @ApiPropertyOptional({
    description: 'Search by current version (partial match)',
    example: '2.0',
  })
  @IsOptional()
  @IsString()
  current_version?: string;

  @ApiPropertyOptional({
    description: 'Filter by release date from (YYYY-MM-DD)',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDateString()
  release_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter by release date to (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  release_date_to?: string;

  @ApiPropertyOptional({
    description: 'Search in programming languages (searches within the JSON array)',
    example: 'python',
  })
  @IsOptional()
  @IsString()
  languages_search?: string;

  @ApiPropertyOptional({
    description: 'Search in platform compatibility (searches within the JSON array)',
    example: 'linux',
  })
  @IsOptional()
  @IsString()
  platforms_search?: string;

  @ApiPropertyOptional({
    description: 'Search by license type (partial match)',
    example: 'MIT',
  })
  @IsOptional()
  @IsString()
  license_search?: string;
}
