import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsInt, Min, Max } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Job-specific filters for enhanced search
 */
export class JobFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by employment types',
    type: [String],
    example: ['Full-time', 'Part-time', 'Contract'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  employment_types?: string[];

  @ApiPropertyOptional({
    description: 'Filter by experience levels',
    type: [String],
    example: ['Entry', 'Mid', 'Senior', 'Lead'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  experience_levels?: string[];

  @ApiPropertyOptional({
    description: 'Filter by location types',
    type: [String],
    example: ['Remote', 'On-site', 'Hybrid'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  location_types?: string[];

  @ApiPropertyOptional({
    description: 'Search by company name (partial match)',
    example: 'Google',
  })
  @IsOptional()
  @IsString()
  company_name?: string;

  @ApiPropertyOptional({
    description: 'Search by job title (partial match)',
    example: 'AI Engineer',
  })
  @IsOptional()
  @IsString()
  job_title?: string;

  @ApiPropertyOptional({
    description: 'Minimum salary (in thousands, e.g., 80 for $80k)',
    type: Number,
    minimum: 0,
    maximum: 1000,
    example: 80,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @Max(1000)
  salary_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum salary (in thousands, e.g., 150 for $150k)',
    type: Number,
    minimum: 0,
    maximum: 1000,
    example: 150,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @Max(1000)
  salary_max?: number;

  @ApiPropertyOptional({
    description: 'Search in job description (partial match)',
    example: 'machine learning',
  })
  @IsOptional()
  @IsString()
  job_description?: string;

  @ApiPropertyOptional({
    description: 'Filter jobs that have application URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  has_application_url?: boolean;
}
