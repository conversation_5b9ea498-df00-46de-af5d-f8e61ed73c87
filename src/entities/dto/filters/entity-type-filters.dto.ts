import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';

// Import individual filter DTOs
import { CourseFiltersDto } from './course-filters.dto';
import { JobFiltersDto } from './job-filters.dto';
import { HardwareFiltersDto } from './hardware-filters.dto';
import { EventFiltersDto } from './event-filters.dto';
import { ToolFiltersDto } from './tool-filters.dto';
import { AgencyFiltersDto } from './agency-filters.dto';
import { SoftwareFiltersDto } from './software-filters.dto';
import { ResearchPaperFiltersDto } from './research-paper-filters.dto';
import { PodcastFiltersDto } from './podcast-filters.dto';
import { CommunityFiltersDto } from './community-filters.dto';
import { GrantFiltersDto } from './grant-filters.dto';
import { NewsletterFiltersDto } from './newsletter-filters.dto';
import { BookFiltersDto } from './book-filters.dto';

/**
 * Entity Type Specific Filters
 * Allows filtering by fields specific to each entity type
 */
export class EntityTypeFiltersDto {
  @ApiPropertyOptional({
    description: 'Filters specific to Course entities',
    type: CourseFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CourseFiltersDto)
  course?: CourseFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Job entities',
    type: JobFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => JobFiltersDto)
  job?: JobFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Hardware entities',
    type: HardwareFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => HardwareFiltersDto)
  hardware?: HardwareFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Event entities',
    type: EventFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EventFiltersDto)
  event?: EventFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Tool entities',
    type: ToolFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ToolFiltersDto)
  tool?: ToolFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Agency entities',
    type: AgencyFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AgencyFiltersDto)
  agency?: AgencyFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Software entities',
    type: SoftwareFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SoftwareFiltersDto)
  software?: SoftwareFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Research Paper entities',
    type: ResearchPaperFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ResearchPaperFiltersDto)
  research_paper?: ResearchPaperFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Podcast entities',
    type: PodcastFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PodcastFiltersDto)
  podcast?: PodcastFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Community entities',
    type: CommunityFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CommunityFiltersDto)
  community?: CommunityFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Grant entities',
    type: GrantFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GrantFiltersDto)
  grant?: GrantFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Newsletter entities',
    type: NewsletterFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => NewsletterFiltersDto)
  newsletter?: NewsletterFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Book entities',
    type: BookFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BookFiltersDto)
  book?: BookFiltersDto;
}
