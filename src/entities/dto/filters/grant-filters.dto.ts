import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsBoolean, IsDateString, IsInt, Min } from 'class-validator';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

/**
 * Grant-specific filters for enhanced AI grant discovery
 */
export class GrantFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by funding organization',
    type: [String],
    example: ['NSF', 'NIH', 'DARPA', 'EU Horizon', 'Google Research'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  funding_organizations?: string[];

  @ApiPropertyOptional({
    description: 'Filter by research areas (searches within the JSON array)',
    type: [String],
    example: ['AI Safety', 'Machine Learning', 'Robotics', 'NLP'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  research_areas?: string[];

  @ApiPropertyOptional({
    description: 'Filter by eligible applicants (searches within the JSON array)',
    type: [String],
    example: ['Universities', 'Startups', 'Non-profits', 'Individuals'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  eligible_applicants?: string[];

  @ApiPropertyOptional({
    description: 'Minimum funding amount (in thousands, e.g., 50 for $50k)',
    type: Number,
    minimum: 0,
    example: 50,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  funding_amount_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum funding amount (in thousands, e.g., 1000 for $1M)',
    type: Number,
    minimum: 0,
    example: 1000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  funding_amount_max?: number;

  @ApiPropertyOptional({
    description: 'Filter by application deadline from (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  deadline_from?: string;

  @ApiPropertyOptional({
    description: 'Filter by application deadline to (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  deadline_to?: string;

  @ApiPropertyOptional({
    description: 'Filter grants that are currently open for applications',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  is_open?: boolean;

  @ApiPropertyOptional({
    description: 'Filter grants that support international applicants',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  supports_international?: boolean;

  @ApiPropertyOptional({
    description: 'Search in research areas (searches within the JSON array)',
    example: 'machine learning',
  })
  @IsOptional()
  @IsString()
  research_areas_search?: string;

  @ApiPropertyOptional({
    description: 'Search by funding organization (partial match)',
    example: 'nsf',
  })
  @IsOptional()
  @IsString()
  organization_search?: string;

  @ApiPropertyOptional({
    description: 'Search in eligible applicants (searches within the JSON array)',
    example: 'startup',
  })
  @IsOptional()
  @IsString()
  applicants_search?: string;
}
