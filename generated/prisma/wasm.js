
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  role: 'role',
  status: 'status',
  technicalLevel: 'technicalLevel',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio',
  socialLinks: 'socialLinks',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLogin: 'lastLogin',
  bookmarksCount: 'bookmarksCount',
  reputationScore: 'reputationScore',
  requestsFulfilled: 'requestsFulfilled',
  requestsMade: 'requestsMade',
  reviewsCount: 'reviewsCount',
  toolsApproved: 'toolsApproved',
  toolsSubmitted: 'toolsSubmitted'
};

exports.Prisma.EntityTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityScalarFieldEnum = {
  id: 'id',
  entityTypeId: 'entityTypeId',
  name: 'name',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  websiteUrl: 'websiteUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  foundedYear: 'foundedYear',
  socialLinks: 'socialLinks',
  status: 'status',
  avgRating: 'avgRating',
  reviewCount: 'reviewCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  legacyId: 'legacyId',
  submitterId: 'submitterId',
  affiliateStatus: 'affiliateStatus',
  locationSummary: 'locationSummary',
  metaDescription: 'metaDescription',
  metaTitle: 'metaTitle',
  refLink: 'refLink',
  scrapedReviewCount: 'scrapedReviewCount',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  scrapedReviewSentimentScore: 'scrapedReviewSentimentScore',
  employeeCountRange: 'employeeCountRange',
  fundingStage: 'fundingStage',
  slug: 'slug'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  parentId: 'parentId'
};

exports.Prisma.TagScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityTagScalarFieldEnum = {
  entityId: 'entityId',
  tagId: 'tagId',
  assignedBy: 'assignedBy',
  createdAt: 'createdAt',
  id: 'id'
};

exports.Prisma.EntityCategoryScalarFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId',
  assignedAt: 'assignedAt',
  assignedBy: 'assignedBy'
};

exports.Prisma.ReviewScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  rating: 'rating',
  title: 'title',
  status: 'status',
  moderationNotes: 'moderationNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  content: 'content',
  downvotes: 'downvotes',
  moderatorId: 'moderatorId',
  upvotes: 'upvotes'
};

exports.Prisma.ReviewVoteScalarFieldEnum = {
  reviewId: 'reviewId',
  userId: 'userId',
  createdAt: 'createdAt',
  id: 'id',
  isUpvote: 'isUpvote'
};

exports.Prisma.EntityDetailsToolScalarFieldEnum = {
  entityId: 'entityId',
  learningCurve: 'learningCurve',
  targetAudience: 'targetAudience',
  keyFeatures: 'keyFeatures',
  useCases: 'useCases',
  pricingModel: 'pricingModel',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  hasFreeTier: 'hasFreeTier',
  integrations: 'integrations',
  apiAccess: 'apiAccess',
  communityUrl: 'communityUrl',
  customizationLevel: 'customizationLevel',
  demoAvailable: 'demoAvailable',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  hasLiveChat: 'hasLiveChat',
  libraries: 'libraries',
  mobileSupport: 'mobileSupport',
  openSource: 'openSource',
  programmingLanguages: 'programmingLanguages',
  supportChannels: 'supportChannels',
  supportEmail: 'supportEmail',
  supportedOs: 'supportedOs',
  trialAvailable: 'trialAvailable',
  hasApi: 'hasApi',
  id: 'id',
  platforms: 'platforms',
  technicalLevel: 'technicalLevel'
};

exports.Prisma.EntityDetailsAgencyScalarFieldEnum = {
  entityId: 'entityId',
  servicesOffered: 'servicesOffered',
  industryFocus: 'industryFocus',
  targetClientSize: 'targetClientSize',
  targetAudience: 'targetAudience',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  id: 'id'
};

exports.Prisma.EntityDetailsContentCreatorScalarFieldEnum = {
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  focusAreas: 'focusAreas',
  followerCount: 'followerCount',
  exampleContentUrl: 'exampleContentUrl',
  id: 'id'
};

exports.Prisma.EntityDetailsCommunityScalarFieldEnum = {
  entityId: 'entityId',
  platform: 'platform',
  memberCount: 'memberCount',
  focusTopics: 'focusTopics',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl',
  id: 'id'
};

exports.Prisma.EntityDetailsNewsletterScalarFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  mainTopics: 'mainTopics',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName',
  subscriberCount: 'subscriberCount',
  id: 'id'
};

exports.Prisma.EntityDetailsCourseScalarFieldEnum = {
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  skillLevel: 'skillLevel',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl',
  enrollmentCount: 'enrollmentCount',
  certificateAvailable: 'certificateAvailable',
  id: 'id'
};

exports.Prisma.UserSavedEntityScalarFieldEnum = {
  userId: 'userId',
  entityId: 'entityId',
  createdAt: 'createdAt',
  id: 'id'
};

exports.Prisma.UserFollowedTagScalarFieldEnum = {
  userId: 'userId',
  tagId: 'tagId',
  createdAt: 'createdAt',
  id: 'id'
};

exports.Prisma.UserFollowedCategoryScalarFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId',
  followedAt: 'followedAt'
};

exports.Prisma.UserActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  actionType: 'actionType',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId',
  details: 'details',
  createdAt: 'createdAt'
};

exports.Prisma.UserNotificationSettingsScalarFieldEnum = {
  userId: 'userId',
  emailNewsletter: 'emailNewsletter',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  emailMarketing: 'emailMarketing',
  emailOnNewEntityInFollowed: 'emailOnNewEntityInFollowed',
  emailOnNewFollower: 'emailOnNewFollower',
  emailOnNewReview: 'emailOnNewReview',
  emailOnReviewResponse: 'emailOnReviewResponse',
  id: 'id'
};

exports.Prisma.BadgeTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  iconUrl: 'iconUrl',
  scope: 'scope',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  criteria: 'criteria'
};

exports.Prisma.UserBadgeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  notes: 'notes',
  grantedBy: 'grantedBy'
};

exports.Prisma.EntityBadgeScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  notes: 'notes',
  expiresAt: 'expiresAt',
  grantedBy: 'grantedBy'
};

exports.Prisma.EntityDetailsDatasetScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  accessNotes: 'accessNotes',
  description: 'description',
  license: 'license',
  sizeInBytes: 'sizeInBytes',
  sourceUrl: 'sourceUrl',
  collectionMethod: 'collectionMethod',
  id: 'id',
  updateFrequency: 'updateFrequency',
  format: 'format'
};

exports.Prisma.EntityDetailsResearchPaperScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  abstract: 'abstract',
  citationCount: 'citationCount',
  doi: 'doi',
  journalOrConference: 'journalOrConference',
  publicationDate: 'publicationDate',
  id: 'id',
  pdfUrl: 'pdfUrl',
  authors: 'authors'
};

exports.Prisma.EntityDetailsSoftwareScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  currentVersion: 'currentVersion',
  licenseType: 'licenseType',
  communityUrl: 'communityUrl',
  hasFreeTier: 'hasFreeTier',
  hasLiveChat: 'hasLiveChat',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingModel: 'pricingModel',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  apiAccess: 'apiAccess',
  customizationLevel: 'customizationLevel',
  demoAvailable: 'demoAvailable',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  hasApi: 'hasApi',
  id: 'id',
  keyFeatures: 'keyFeatures',
  libraries: 'libraries',
  mobileSupport: 'mobileSupport',
  openSource: 'openSource',
  supportChannels: 'supportChannels',
  supportedOs: 'supportedOs',
  targetAudience: 'targetAudience',
  trialAvailable: 'trialAvailable',
  integrations: 'integrations',
  platformCompatibility: 'platformCompatibility',
  programmingLanguages: 'programmingLanguages',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsModelScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  license: 'license',
  modelArchitecture: 'modelArchitecture',
  trainingDataset: 'trainingDataset',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  id: 'id',
  inputDataTypes: 'inputDataTypes',
  libraries: 'libraries',
  outputDataTypes: 'outputDataTypes',
  performanceMetrics: 'performanceMetrics',
  targetAudience: 'targetAudience',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsProjectReferenceScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  forks: 'forks',
  id: 'id',
  keyTechnologies: 'keyTechnologies',
  license: 'license',
  repositoryUrl: 'repositoryUrl',
  stars: 'stars',
  status: 'status',
  useCases: 'useCases',
  contributors: 'contributors'
};

exports.Prisma.EntityDetailsServiceProviderScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  companySizeFocus: 'companySizeFocus',
  id: 'id',
  industrySpecializations: 'industrySpecializations',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  servicesOffered: 'servicesOffered',
  targetAudience: 'targetAudience'
};

exports.Prisma.EntityDetailsInvestorScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  contactEmail: 'contactEmail',
  applicationUrl: 'applicationUrl',
  focusAreas: 'focusAreas',
  id: 'id',
  investmentStages: 'investmentStages',
  investorType: 'investorType',
  locationSummary: 'locationSummary',
  notableInvestments: 'notableInvestments',
  portfolioSize: 'portfolioSize'
};

exports.Prisma.EntityDetailsEventScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  endDate: 'endDate',
  location: 'location',
  price: 'price',
  registrationUrl: 'registrationUrl',
  startDate: 'startDate',
  eventType: 'eventType',
  id: 'id',
  isOnline: 'isOnline',
  keySpeakers: 'keySpeakers',
  targetAudience: 'targetAudience',
  topics: 'topics'
};

exports.Prisma.EntityDetailsJobScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  applicationUrl: 'applicationUrl',
  companyName: 'companyName',
  experienceLevel: 'experienceLevel',
  id: 'id',
  isRemote: 'isRemote',
  jobType: 'jobType',
  keyResponsibilities: 'keyResponsibilities',
  location: 'location',
  requiredSkills: 'requiredSkills',
  salaryMax: 'salaryMax',
  salaryMin: 'salaryMin'
};

exports.Prisma.EntityDetailsGrantScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  applicationUrl: 'applicationUrl',
  amount: 'amount',
  deadline: 'deadline',
  eligibility: 'eligibility',
  focusAreas: 'focusAreas',
  funderName: 'funderName',
  grantType: 'grantType',
  id: 'id',
  location: 'location'
};

exports.Prisma.EntityDetailsBountyScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  deadline: 'deadline',
  id: 'id',
  platform: 'platform',
  requiredSkills: 'requiredSkills',
  status: 'status',
  taskDescription: 'taskDescription',
  url: 'url'
};

exports.Prisma.EntityDetailsHardwareScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  availability: 'availability',
  gpu: 'gpu',
  id: 'id',
  memory: 'memory',
  powerConsumption: 'powerConsumption',
  price: 'price',
  processor: 'processor',
  storage: 'storage',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsNewsScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  articleUrl: 'articleUrl',
  author: 'author',
  publicationDate: 'publicationDate',
  sourceName: 'sourceName',
  summary: 'summary',
  id: 'id',
  tags: 'tags'
};

exports.Prisma.EntityDetailsBookScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isbn: 'isbn',
  pageCount: 'pageCount',
  publisher: 'publisher',
  purchaseUrl: 'purchaseUrl',
  summary: 'summary',
  author: 'author',
  format: 'format',
  id: 'id',
  publicationDate: 'publicationDate'
};

exports.Prisma.EntityDetailsPodcastScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  frequency: 'frequency',
  applePodcastsUrl: 'applePodcastsUrl',
  averageLength: 'averageLength',
  googlePodcastsUrl: 'googlePodcastsUrl',
  host: 'host',
  id: 'id',
  mainTopics: 'mainTopics',
  spotifyUrl: 'spotifyUrl',
  youtubeUrl: 'youtubeUrl'
};

exports.Prisma.EntityDetailsPlatformScalarFieldEnum = {
  entityId: 'entityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  documentationUrl: 'documentationUrl',
  platformType: 'platformType',
  communityUrl: 'communityUrl',
  hasFreeTier: 'hasFreeTier',
  hasLiveChat: 'hasLiveChat',
  priceRange: 'priceRange',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  pricingModel: 'pricingModel',
  apiAccess: 'apiAccess',
  customizationLevel: 'customizationLevel',
  demoAvailable: 'demoAvailable',
  deploymentOptions: 'deploymentOptions',
  hasApi: 'hasApi',
  id: 'id',
  mobileSupport: 'mobileSupport',
  openSource: 'openSource',
  supportChannels: 'supportChannels',
  supportedOs: 'supportedOs',
  targetAudience: 'targetAudience',
  trialAvailable: 'trialAvailable',
  integrations: 'integrations',
  keyServices: 'keyServices',
  useCases: 'useCases'
};

exports.Prisma.EntityFeatureScalarFieldEnum = {
  entityId: 'entityId',
  featureId: 'featureId',
  assignedBy: 'assignedBy',
  createdAt: 'createdAt',
  id: 'id'
};

exports.Prisma.AppSettingScalarFieldEnum = {
  key: 'key',
  value: 'value',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FeatureScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  iconUrl: 'iconUrl'
};

exports.Prisma.UserPreferencesScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  emailNotifications: 'emailNotifications',
  marketingEmails: 'marketingEmails',
  weeklyDigest: 'weeklyDigest',
  newToolAlerts: 'newToolAlerts',
  profileVisibility: 'profileVisibility',
  showBookmarks: 'showBookmarks',
  showReviews: 'showReviews',
  showActivity: 'showActivity',
  theme: 'theme',
  itemsPerPage: 'itemsPerPage',
  defaultView: 'defaultView',
  preferredCategories: 'preferredCategories',
  blockedCategories: 'blockedCategories',
  contentLanguage: 'contentLanguage',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ToolRequestScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  toolName: 'toolName',
  description: 'description',
  reason: 'reason',
  categorySuggestion: 'categorySuggestion',
  websiteUrl: 'websiteUrl',
  priority: 'priority',
  status: 'status',
  adminNotes: 'adminNotes',
  votes: 'votes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSubmittedToolScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  submissionStatus: 'submissionStatus',
  submittedAt: 'submittedAt',
  reviewedAt: 'reviewedAt',
  reviewerId: 'reviewerId',
  reviewerNotes: 'reviewerNotes',
  changesRequested: 'changesRequested'
};

exports.Prisma.ProfileActivityScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  description: 'description',
  entityId: 'entityId',
  entityName: 'entityName',
  entitySlug: 'entitySlug',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio'
};

exports.Prisma.EntityTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl'
};

exports.Prisma.EntityOrderByRelevanceFieldEnum = {
  id: 'id',
  entityTypeId: 'entityTypeId',
  name: 'name',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  websiteUrl: 'websiteUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  legacyId: 'legacyId',
  submitterId: 'submitterId',
  locationSummary: 'locationSummary',
  metaDescription: 'metaDescription',
  metaTitle: 'metaTitle',
  refLink: 'refLink',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  slug: 'slug'
};

exports.Prisma.CategoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  parentId: 'parentId'
};

exports.Prisma.TagOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug'
};

exports.Prisma.EntityTagOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  tagId: 'tagId',
  assignedBy: 'assignedBy',
  id: 'id'
};

exports.Prisma.EntityCategoryOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId',
  assignedBy: 'assignedBy'
};

exports.Prisma.ReviewOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  title: 'title',
  moderationNotes: 'moderationNotes',
  content: 'content',
  moderatorId: 'moderatorId'
};

exports.Prisma.ReviewVoteOrderByRelevanceFieldEnum = {
  reviewId: 'reviewId',
  userId: 'userId',
  id: 'id'
};

exports.Prisma.EntityDetailsToolOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  communityUrl: 'communityUrl',
  customizationLevel: 'customizationLevel',
  supportEmail: 'supportEmail',
  id: 'id'
};

exports.Prisma.EntityDetailsAgencyOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  id: 'id'
};

exports.Prisma.EntityDetailsContentCreatorOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  exampleContentUrl: 'exampleContentUrl',
  id: 'id'
};

exports.Prisma.EntityDetailsCommunityOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  platform: 'platform',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl',
  id: 'id'
};

exports.Prisma.EntityDetailsNewsletterOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName',
  id: 'id'
};

exports.Prisma.EntityDetailsCourseOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl',
  id: 'id'
};

exports.Prisma.UserSavedEntityOrderByRelevanceFieldEnum = {
  userId: 'userId',
  entityId: 'entityId',
  id: 'id'
};

exports.Prisma.UserFollowedTagOrderByRelevanceFieldEnum = {
  userId: 'userId',
  tagId: 'tagId',
  id: 'id'
};

exports.Prisma.UserFollowedCategoryOrderByRelevanceFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId'
};

exports.Prisma.UserActivityLogOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId'
};

exports.Prisma.UserNotificationSettingsOrderByRelevanceFieldEnum = {
  userId: 'userId',
  id: 'id'
};

exports.Prisma.BadgeTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  iconUrl: 'iconUrl'
};

exports.Prisma.UserBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  notes: 'notes',
  grantedBy: 'grantedBy'
};

exports.Prisma.EntityBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  notes: 'notes',
  grantedBy: 'grantedBy'
};

exports.Prisma.EntityDetailsDatasetOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  accessNotes: 'accessNotes',
  description: 'description',
  license: 'license',
  sourceUrl: 'sourceUrl',
  collectionMethod: 'collectionMethod',
  id: 'id',
  updateFrequency: 'updateFrequency',
  format: 'format'
};

exports.Prisma.EntityDetailsResearchPaperOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  abstract: 'abstract',
  doi: 'doi',
  journalOrConference: 'journalOrConference',
  id: 'id',
  pdfUrl: 'pdfUrl',
  authors: 'authors'
};

exports.Prisma.EntityDetailsSoftwareOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  currentVersion: 'currentVersion',
  licenseType: 'licenseType',
  communityUrl: 'communityUrl',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  customizationLevel: 'customizationLevel',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  id: 'id',
  keyFeatures: 'keyFeatures',
  libraries: 'libraries',
  supportChannels: 'supportChannels',
  supportedOs: 'supportedOs',
  targetAudience: 'targetAudience',
  integrations: 'integrations',
  platformCompatibility: 'platformCompatibility',
  programmingLanguages: 'programmingLanguages',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsModelOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  license: 'license',
  modelArchitecture: 'modelArchitecture',
  trainingDataset: 'trainingDataset',
  deploymentOptions: 'deploymentOptions',
  frameworks: 'frameworks',
  id: 'id',
  inputDataTypes: 'inputDataTypes',
  libraries: 'libraries',
  outputDataTypes: 'outputDataTypes',
  targetAudience: 'targetAudience',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsProjectReferenceOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  id: 'id',
  keyTechnologies: 'keyTechnologies',
  license: 'license',
  repositoryUrl: 'repositoryUrl',
  status: 'status',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsServiceProviderOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  companySizeFocus: 'companySizeFocus',
  id: 'id',
  industrySpecializations: 'industrySpecializations',
  locationSummary: 'locationSummary',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  servicesOffered: 'servicesOffered',
  targetAudience: 'targetAudience'
};

exports.Prisma.EntityDetailsInvestorOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  contactEmail: 'contactEmail',
  applicationUrl: 'applicationUrl',
  focusAreas: 'focusAreas',
  id: 'id',
  investmentStages: 'investmentStages',
  investorType: 'investorType',
  locationSummary: 'locationSummary',
  notableInvestments: 'notableInvestments'
};

exports.Prisma.EntityDetailsEventOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  location: 'location',
  price: 'price',
  registrationUrl: 'registrationUrl',
  eventType: 'eventType',
  id: 'id',
  keySpeakers: 'keySpeakers',
  targetAudience: 'targetAudience',
  topics: 'topics'
};

exports.Prisma.EntityDetailsJobOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  applicationUrl: 'applicationUrl',
  companyName: 'companyName',
  experienceLevel: 'experienceLevel',
  id: 'id',
  jobType: 'jobType',
  keyResponsibilities: 'keyResponsibilities',
  location: 'location',
  requiredSkills: 'requiredSkills'
};

exports.Prisma.EntityDetailsGrantOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  applicationUrl: 'applicationUrl',
  amount: 'amount',
  eligibility: 'eligibility',
  focusAreas: 'focusAreas',
  funderName: 'funderName',
  grantType: 'grantType',
  id: 'id',
  location: 'location'
};

exports.Prisma.EntityDetailsBountyOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  amount: 'amount',
  id: 'id',
  platform: 'platform',
  requiredSkills: 'requiredSkills',
  status: 'status',
  taskDescription: 'taskDescription',
  url: 'url'
};

exports.Prisma.EntityDetailsHardwareOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  availability: 'availability',
  gpu: 'gpu',
  id: 'id',
  memory: 'memory',
  powerConsumption: 'powerConsumption',
  price: 'price',
  processor: 'processor',
  storage: 'storage',
  useCases: 'useCases'
};

exports.Prisma.EntityDetailsNewsOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  articleUrl: 'articleUrl',
  author: 'author',
  sourceName: 'sourceName',
  summary: 'summary',
  id: 'id',
  tags: 'tags'
};

exports.Prisma.EntityDetailsBookOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  isbn: 'isbn',
  publisher: 'publisher',
  purchaseUrl: 'purchaseUrl',
  summary: 'summary',
  author: 'author',
  format: 'format',
  id: 'id'
};

exports.Prisma.EntityDetailsPodcastOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  frequency: 'frequency',
  applePodcastsUrl: 'applePodcastsUrl',
  averageLength: 'averageLength',
  googlePodcastsUrl: 'googlePodcastsUrl',
  host: 'host',
  id: 'id',
  mainTopics: 'mainTopics',
  spotifyUrl: 'spotifyUrl',
  youtubeUrl: 'youtubeUrl'
};

exports.Prisma.EntityDetailsPlatformOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  documentationUrl: 'documentationUrl',
  platformType: 'platformType',
  communityUrl: 'communityUrl',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  customizationLevel: 'customizationLevel',
  deploymentOptions: 'deploymentOptions',
  id: 'id',
  supportChannels: 'supportChannels',
  supportedOs: 'supportedOs',
  targetAudience: 'targetAudience',
  integrations: 'integrations',
  keyServices: 'keyServices',
  useCases: 'useCases'
};

exports.Prisma.EntityFeatureOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  featureId: 'featureId',
  assignedBy: 'assignedBy',
  id: 'id'
};

exports.Prisma.AppSettingOrderByRelevanceFieldEnum = {
  key: 'key',
  value: 'value',
  description: 'description'
};

exports.Prisma.FeatureOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl'
};

exports.Prisma.UserPreferencesOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  preferredCategories: 'preferredCategories',
  blockedCategories: 'blockedCategories',
  contentLanguage: 'contentLanguage'
};

exports.Prisma.ToolRequestOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  toolName: 'toolName',
  description: 'description',
  reason: 'reason',
  categorySuggestion: 'categorySuggestion',
  websiteUrl: 'websiteUrl',
  adminNotes: 'adminNotes'
};

exports.Prisma.UserSubmittedToolOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  reviewerId: 'reviewerId',
  reviewerNotes: 'reviewerNotes',
  changesRequested: 'changesRequested'
};

exports.Prisma.ProfileActivityOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  description: 'description',
  entityId: 'entityId',
  entityName: 'entityName',
  entitySlug: 'entitySlug'
};
exports.UserRole = exports.$Enums.UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  MODERATOR: 'MODERATOR'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  SUSPENDED: 'SUSPENDED',
  DELETED: 'DELETED'
};

exports.TechnicalLevel = exports.$Enums.TechnicalLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.EntityStatus = exports.$Enums.EntityStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  REJECTED: 'REJECTED',
  INACTIVE: 'INACTIVE',
  ARCHIVED: 'ARCHIVED',
  NEEDS_REVISION: 'NEEDS_REVISION'
};

exports.AffiliateStatus = exports.$Enums.AffiliateStatus = {
  NONE: 'NONE',
  APPLIED: 'APPLIED',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.EmployeeCountRange = exports.$Enums.EmployeeCountRange = {
  C1_10: 'C1_10',
  C11_50: 'C11_50',
  C51_200: 'C51_200',
  C201_500: 'C201_500',
  C501_1000: 'C501_1000',
  C1001_5000: 'C1001_5000',
  C5001_PLUS: 'C5001_PLUS'
};

exports.FundingStage = exports.$Enums.FundingStage = {
  SEED: 'SEED',
  PRE_SEED: 'PRE_SEED',
  SERIES_A: 'SERIES_A',
  SERIES_B: 'SERIES_B',
  SERIES_C: 'SERIES_C',
  SERIES_D_PLUS: 'SERIES_D_PLUS',
  PUBLIC: 'PUBLIC'
};

exports.ReviewStatus = exports.$Enums.ReviewStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.LearningCurve = exports.$Enums.LearningCurve = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH'
};

exports.PricingModel = exports.$Enums.PricingModel = {
  FREE: 'FREE',
  FREEMIUM: 'FREEMIUM',
  SUBSCRIPTION: 'SUBSCRIPTION',
  PAY_PER_USE: 'PAY_PER_USE',
  ONE_TIME_PURCHASE: 'ONE_TIME_PURCHASE',
  CONTACT_SALES: 'CONTACT_SALES',
  OPEN_SOURCE: 'OPEN_SOURCE'
};

exports.PriceRange = exports.$Enums.PriceRange = {
  FREE: 'FREE',
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  ENTERPRISE: 'ENTERPRISE'
};

exports.SkillLevel = exports.$Enums.SkillLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.ActionType = exports.$Enums.ActionType = {
  VIEW_ENTITY: 'VIEW_ENTITY',
  CLICK_ENTITY_LINK: 'CLICK_ENTITY_LINK',
  SAVE_ENTITY: 'SAVE_ENTITY',
  UNSAVE_ENTITY: 'UNSAVE_ENTITY',
  SUBMIT_REVIEW: 'SUBMIT_REVIEW',
  VOTE_REVIEW: 'VOTE_REVIEW',
  FOLLOW_TAG: 'FOLLOW_TAG',
  UNFOLLOW_TAG: 'UNFOLLOW_TAG',
  FOLLOW_CATEGORY: 'FOLLOW_CATEGORY',
  UNFOLLOW_CATEGORY: 'UNFOLLOW_CATEGORY',
  SEARCH: 'SEARCH',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  SIGNUP: 'SIGNUP',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  GRANT_BADGE: 'GRANT_BADGE',
  REVOKE_BADGE: 'REVOKE_BADGE'
};

exports.BadgeScope = exports.$Enums.BadgeScope = {
  USER: 'USER',
  ENTITY: 'ENTITY'
};

exports.ProfileVisibility = exports.$Enums.ProfileVisibility = {
  PUBLIC: 'PUBLIC',
  PRIVATE: 'PRIVATE',
  FRIENDS: 'FRIENDS'
};

exports.Theme = exports.$Enums.Theme = {
  LIGHT: 'LIGHT',
  DARK: 'DARK',
  SYSTEM: 'SYSTEM'
};

exports.DefaultView = exports.$Enums.DefaultView = {
  GRID: 'GRID',
  LIST: 'LIST'
};

exports.ToolRequestPriority = exports.$Enums.ToolRequestPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH'
};

exports.ToolRequestStatus = exports.$Enums.ToolRequestStatus = {
  PENDING: 'PENDING',
  UNDER_REVIEW: 'UNDER_REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  COMPLETED: 'COMPLETED'
};

exports.SubmissionStatus = exports.$Enums.SubmissionStatus = {
  PENDING: 'PENDING',
  UNDER_REVIEW: 'UNDER_REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  PUBLISHED: 'PUBLISHED'
};

exports.ProfileActivityType = exports.$Enums.ProfileActivityType = {
  BOOKMARK: 'BOOKMARK',
  REVIEW: 'REVIEW',
  SUBMISSION: 'SUBMISSION',
  REQUEST: 'REQUEST',
  VOTE: 'VOTE'
};

exports.Prisma.ModelName = {
  User: 'User',
  EntityType: 'EntityType',
  Entity: 'Entity',
  Category: 'Category',
  Tag: 'Tag',
  EntityTag: 'EntityTag',
  EntityCategory: 'EntityCategory',
  Review: 'Review',
  ReviewVote: 'ReviewVote',
  EntityDetailsTool: 'EntityDetailsTool',
  EntityDetailsAgency: 'EntityDetailsAgency',
  EntityDetailsContentCreator: 'EntityDetailsContentCreator',
  EntityDetailsCommunity: 'EntityDetailsCommunity',
  EntityDetailsNewsletter: 'EntityDetailsNewsletter',
  EntityDetailsCourse: 'EntityDetailsCourse',
  UserSavedEntity: 'UserSavedEntity',
  UserFollowedTag: 'UserFollowedTag',
  UserFollowedCategory: 'UserFollowedCategory',
  UserActivityLog: 'UserActivityLog',
  UserNotificationSettings: 'UserNotificationSettings',
  BadgeType: 'BadgeType',
  UserBadge: 'UserBadge',
  EntityBadge: 'EntityBadge',
  EntityDetailsDataset: 'EntityDetailsDataset',
  EntityDetailsResearchPaper: 'EntityDetailsResearchPaper',
  EntityDetailsSoftware: 'EntityDetailsSoftware',
  EntityDetailsModel: 'EntityDetailsModel',
  EntityDetailsProjectReference: 'EntityDetailsProjectReference',
  EntityDetailsServiceProvider: 'EntityDetailsServiceProvider',
  EntityDetailsInvestor: 'EntityDetailsInvestor',
  EntityDetailsEvent: 'EntityDetailsEvent',
  EntityDetailsJob: 'EntityDetailsJob',
  EntityDetailsGrant: 'EntityDetailsGrant',
  EntityDetailsBounty: 'EntityDetailsBounty',
  EntityDetailsHardware: 'EntityDetailsHardware',
  EntityDetailsNews: 'EntityDetailsNews',
  EntityDetailsBook: 'EntityDetailsBook',
  EntityDetailsPodcast: 'EntityDetailsPodcast',
  EntityDetailsPlatform: 'EntityDetailsPlatform',
  EntityFeature: 'EntityFeature',
  AppSetting: 'AppSetting',
  Feature: 'Feature',
  UserPreferences: 'UserPreferences',
  ToolRequest: 'ToolRequest',
  UserSubmittedTool: 'UserSubmittedTool',
  ProfileActivity: 'ProfileActivity'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
