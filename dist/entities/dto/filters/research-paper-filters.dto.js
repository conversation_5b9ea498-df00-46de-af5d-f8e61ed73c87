"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResearchPaperFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class ResearchPaperFiltersDto {
}
exports.ResearchPaperFiltersDto = ResearchPaperFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by authors (searches within the JSON array)',
        type: [String],
        example: ['Geoffrey Hinton', 'Yann LeCun', 'Yoshua Bengio'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ResearchPaperFiltersDto.prototype, "authors", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by research areas (searches within the JSON array)',
        type: [String],
        example: ['Machine Learning', 'Natural Language Processing', 'Computer Vision', 'Robotics'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ResearchPaperFiltersDto.prototype, "research_areas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by publication venues (searches within the JSON array)',
        type: [String],
        example: ['NeurIPS', 'ICML', 'ICLR', 'AAAI', 'Nature'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ResearchPaperFiltersDto.prototype, "publication_venues", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by keywords (searches within the JSON array)',
        type: [String],
        example: ['transformer', 'attention', 'neural network', 'deep learning'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ResearchPaperFiltersDto.prototype, "keywords", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by DOI (partial match)',
        example: '10.1038',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ResearchPaperFiltersDto.prototype, "doi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by publication date from (YYYY-MM-DD)',
        example: '2020-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ResearchPaperFiltersDto.prototype, "publication_date_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by publication date to (YYYY-MM-DD)',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ResearchPaperFiltersDto.prototype, "publication_date_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum citation count',
        type: Number,
        minimum: 0,
        example: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ResearchPaperFiltersDto.prototype, "citation_count_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum citation count',
        type: Number,
        minimum: 0,
        example: 10000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ResearchPaperFiltersDto.prototype, "citation_count_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in abstract (partial match)',
        example: 'attention mechanism',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ResearchPaperFiltersDto.prototype, "abstract_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in authors (searches within the JSON array)',
        example: 'hinton',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ResearchPaperFiltersDto.prototype, "authors_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in research areas (searches within the JSON array)',
        example: 'nlp',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ResearchPaperFiltersDto.prototype, "research_areas_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in publication venues (searches within the JSON array)',
        example: 'neurips',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ResearchPaperFiltersDto.prototype, "venues_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in keywords (searches within the JSON array)',
        example: 'transformer',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ResearchPaperFiltersDto.prototype, "keywords_search", void 0);
//# sourceMappingURL=research-paper-filters.dto.js.map