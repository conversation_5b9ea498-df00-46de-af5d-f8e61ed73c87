import { CourseFiltersDto } from './course-filters.dto';
import { JobFiltersDto } from './job-filters.dto';
import { HardwareFiltersDto } from './hardware-filters.dto';
import { EventFiltersDto } from './event-filters.dto';
import { ToolFiltersDto } from './tool-filters.dto';
import { AgencyFiltersDto } from './agency-filters.dto';
import { SoftwareFiltersDto } from './software-filters.dto';
import { ResearchPaperFiltersDto } from './research-paper-filters.dto';
import { PodcastFiltersDto } from './podcast-filters.dto';
import { CommunityFiltersDto } from './community-filters.dto';
import { GrantFiltersDto } from './grant-filters.dto';
import { NewsletterFiltersDto } from './newsletter-filters.dto';
import { BookFiltersDto } from './book-filters.dto';
export declare class EntityTypeFiltersDto {
    course?: CourseFiltersDto;
    job?: JobFiltersDto;
    hardware?: HardwareFiltersDto;
    event?: EventFiltersDto;
    tool?: ToolFiltersDto;
    agency?: AgencyFiltersDto;
    software?: SoftwareFiltersDto;
    research_paper?: ResearchPaperFiltersDto;
    podcast?: PodcastFiltersDto;
    community?: CommunityFiltersDto;
    grant?: GrantFiltersDto;
    newsletter?: NewsletterFiltersDto;
    book?: BookFiltersDto;
}
