"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PodcastFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class PodcastFiltersDto {
}
exports.PodcastFiltersDto = PodcastFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by host name (partial match)',
        example: 'Lex Fridman',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PodcastFiltersDto.prototype, "host", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by main topics (searches within the array)',
        type: [String],
        example: ['AI', 'Machine Learning', 'Deep Learning', 'Robotics'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], PodcastFiltersDto.prototype, "main_topics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by frequency',
        type: [String],
        example: ['Weekly', 'Bi-weekly', 'Monthly', 'Daily'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], PodcastFiltersDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by average episode length (partial match)',
        example: '60 minutes',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PodcastFiltersDto.prototype, "average_length", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter podcasts available on Spotify',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PodcastFiltersDto.prototype, "has_spotify", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter podcasts available on Apple Podcasts',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PodcastFiltersDto.prototype, "has_apple_podcasts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter podcasts available on Google Podcasts',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PodcastFiltersDto.prototype, "has_google_podcasts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter podcasts available on YouTube',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PodcastFiltersDto.prototype, "has_youtube", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in main topics (searches within the array)',
        example: 'machine learning',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PodcastFiltersDto.prototype, "topics_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by frequency (partial match)',
        example: 'weekly',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PodcastFiltersDto.prototype, "frequency_search", void 0);
//# sourceMappingURL=podcast-filters.dto.js.map