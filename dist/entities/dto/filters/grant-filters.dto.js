"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrantFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class GrantFiltersDto {
}
exports.GrantFiltersDto = GrantFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by funding organization',
        type: [String],
        example: ['NSF', 'NIH', 'DARPA', 'EU Horizon', 'Google Research'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], GrantFiltersDto.prototype, "funding_organizations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by research areas (searches within the JSON array)',
        type: [String],
        example: ['AI Safety', 'Machine Learning', 'Robotics', 'NLP'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], GrantFiltersDto.prototype, "research_areas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by eligible applicants (searches within the JSON array)',
        type: [String],
        example: ['Universities', 'Startups', 'Non-profits', 'Individuals'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], GrantFiltersDto.prototype, "eligible_applicants", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum funding amount (in thousands, e.g., 50 for $50k)',
        type: Number,
        minimum: 0,
        example: 50,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], GrantFiltersDto.prototype, "funding_amount_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum funding amount (in thousands, e.g., 1000 for $1M)',
        type: Number,
        minimum: 0,
        example: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], GrantFiltersDto.prototype, "funding_amount_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by application deadline from (YYYY-MM-DD)',
        example: '2024-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], GrantFiltersDto.prototype, "deadline_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by application deadline to (YYYY-MM-DD)',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], GrantFiltersDto.prototype, "deadline_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter grants that are currently open for applications',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GrantFiltersDto.prototype, "is_open", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter grants that support international applicants',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GrantFiltersDto.prototype, "supports_international", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in research areas (searches within the JSON array)',
        example: 'machine learning',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GrantFiltersDto.prototype, "research_areas_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by funding organization (partial match)',
        example: 'nsf',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GrantFiltersDto.prototype, "organization_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in eligible applicants (searches within the JSON array)',
        example: 'startup',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GrantFiltersDto.prototype, "applicants_search", void 0);
//# sourceMappingURL=grant-filters.dto.js.map