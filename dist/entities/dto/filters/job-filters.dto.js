"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class JobFiltersDto {
}
exports.JobFiltersDto = JobFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by employment types',
        type: [String],
        example: ['Full-time', 'Part-time', 'Contract'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], JobFiltersDto.prototype, "employment_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by experience levels',
        type: [String],
        example: ['Entry', 'Mid', 'Senior', 'Lead'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], JobFiltersDto.prototype, "experience_levels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by location types',
        type: [String],
        example: ['Remote', 'On-site', 'Hybrid'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], JobFiltersDto.prototype, "location_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by company name (partial match)',
        example: 'Google',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobFiltersDto.prototype, "company_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by job title (partial match)',
        example: 'AI Engineer',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobFiltersDto.prototype, "job_title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum salary (in thousands, e.g., 80 for $80k)',
        type: Number,
        minimum: 0,
        maximum: 1000,
        example: 80,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], JobFiltersDto.prototype, "salary_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum salary (in thousands, e.g., 150 for $150k)',
        type: Number,
        minimum: 0,
        maximum: 1000,
        example: 150,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], JobFiltersDto.prototype, "salary_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in job description (partial match)',
        example: 'machine learning',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobFiltersDto.prototype, "job_description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter jobs that have application URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    __metadata("design:type", Boolean)
], JobFiltersDto.prototype, "has_application_url", void 0);
//# sourceMappingURL=job-filters.dto.js.map