"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoftwareFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class SoftwareFiltersDto {
}
exports.SoftwareFiltersDto = SoftwareFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by license type',
        type: [String],
        example: ['MIT', 'Apache 2.0', 'GPL', 'Commercial', 'Proprietary'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], SoftwareFiltersDto.prototype, "license_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by programming languages (searches within the JSON array)',
        type: [String],
        example: ['Python', 'JavaScript', 'Java', 'C++', 'R'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], SoftwareFiltersDto.prototype, "programming_languages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by platform compatibility (searches within the JSON array)',
        type: [String],
        example: ['Windows', 'macOS', 'Linux', 'Web', 'Mobile'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], SoftwareFiltersDto.prototype, "platform_compatibility", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter software that is open source',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SoftwareFiltersDto.prototype, "open_source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter software that has repository URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SoftwareFiltersDto.prototype, "has_repository", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by current version (partial match)',
        example: '2.0',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SoftwareFiltersDto.prototype, "current_version", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by release date from (YYYY-MM-DD)',
        example: '2023-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], SoftwareFiltersDto.prototype, "release_date_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by release date to (YYYY-MM-DD)',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], SoftwareFiltersDto.prototype, "release_date_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in programming languages (searches within the JSON array)',
        example: 'python',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SoftwareFiltersDto.prototype, "languages_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in platform compatibility (searches within the JSON array)',
        example: 'linux',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SoftwareFiltersDto.prototype, "platforms_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by license type (partial match)',
        example: 'MIT',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SoftwareFiltersDto.prototype, "license_search", void 0);
//# sourceMappingURL=software-filters.dto.js.map