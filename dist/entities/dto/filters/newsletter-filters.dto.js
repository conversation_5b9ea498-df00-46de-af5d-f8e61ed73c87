"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsletterFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class NewsletterFiltersDto {
}
exports.NewsletterFiltersDto = NewsletterFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by newsletter frequency',
        type: [String],
        example: ['Daily', 'Weekly', 'Bi-weekly', 'Monthly'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], NewsletterFiltersDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by focus areas (searches within the JSON array)',
        type: [String],
        example: ['AI News', 'Research', 'Industry Updates', 'Tutorials'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], NewsletterFiltersDto.prototype, "focus_areas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by target audience (searches within the JSON array)',
        type: [String],
        example: ['Developers', 'Researchers', 'Business Leaders', 'Students'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], NewsletterFiltersDto.prototype, "target_audience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum subscriber count',
        type: Number,
        minimum: 0,
        example: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], NewsletterFiltersDto.prototype, "subscriber_count_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum subscriber count',
        type: Number,
        minimum: 0,
        example: 100000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], NewsletterFiltersDto.prototype, "subscriber_count_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter newsletters that are free',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], NewsletterFiltersDto.prototype, "is_free", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter newsletters that have archives available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], NewsletterFiltersDto.prototype, "has_archives", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by author/creator (partial match)',
        example: 'Andrew Ng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NewsletterFiltersDto.prototype, "author", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in focus areas (searches within the JSON array)',
        example: 'machine learning',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NewsletterFiltersDto.prototype, "focus_areas_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in target audience (searches within the JSON array)',
        example: 'developers',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NewsletterFiltersDto.prototype, "audience_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by frequency (partial match)',
        example: 'weekly',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NewsletterFiltersDto.prototype, "frequency_search", void 0);
//# sourceMappingURL=newsletter-filters.dto.js.map