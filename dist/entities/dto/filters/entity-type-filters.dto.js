"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityTypeFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const course_filters_dto_1 = require("./course-filters.dto");
const job_filters_dto_1 = require("./job-filters.dto");
const hardware_filters_dto_1 = require("./hardware-filters.dto");
const event_filters_dto_1 = require("./event-filters.dto");
const tool_filters_dto_1 = require("./tool-filters.dto");
const agency_filters_dto_1 = require("./agency-filters.dto");
const software_filters_dto_1 = require("./software-filters.dto");
const research_paper_filters_dto_1 = require("./research-paper-filters.dto");
const podcast_filters_dto_1 = require("./podcast-filters.dto");
const community_filters_dto_1 = require("./community-filters.dto");
const grant_filters_dto_1 = require("./grant-filters.dto");
const newsletter_filters_dto_1 = require("./newsletter-filters.dto");
const book_filters_dto_1 = require("./book-filters.dto");
class EntityTypeFiltersDto {
}
exports.EntityTypeFiltersDto = EntityTypeFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Course entities',
        type: course_filters_dto_1.CourseFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => course_filters_dto_1.CourseFiltersDto),
    __metadata("design:type", course_filters_dto_1.CourseFiltersDto)
], EntityTypeFiltersDto.prototype, "course", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Job entities',
        type: job_filters_dto_1.JobFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => job_filters_dto_1.JobFiltersDto),
    __metadata("design:type", job_filters_dto_1.JobFiltersDto)
], EntityTypeFiltersDto.prototype, "job", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Hardware entities',
        type: hardware_filters_dto_1.HardwareFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => hardware_filters_dto_1.HardwareFiltersDto),
    __metadata("design:type", hardware_filters_dto_1.HardwareFiltersDto)
], EntityTypeFiltersDto.prototype, "hardware", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Event entities',
        type: event_filters_dto_1.EventFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => event_filters_dto_1.EventFiltersDto),
    __metadata("design:type", event_filters_dto_1.EventFiltersDto)
], EntityTypeFiltersDto.prototype, "event", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Tool entities',
        type: tool_filters_dto_1.ToolFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => tool_filters_dto_1.ToolFiltersDto),
    __metadata("design:type", tool_filters_dto_1.ToolFiltersDto)
], EntityTypeFiltersDto.prototype, "tool", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Agency entities',
        type: agency_filters_dto_1.AgencyFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => agency_filters_dto_1.AgencyFiltersDto),
    __metadata("design:type", agency_filters_dto_1.AgencyFiltersDto)
], EntityTypeFiltersDto.prototype, "agency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Software entities',
        type: software_filters_dto_1.SoftwareFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => software_filters_dto_1.SoftwareFiltersDto),
    __metadata("design:type", software_filters_dto_1.SoftwareFiltersDto)
], EntityTypeFiltersDto.prototype, "software", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Research Paper entities',
        type: research_paper_filters_dto_1.ResearchPaperFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => research_paper_filters_dto_1.ResearchPaperFiltersDto),
    __metadata("design:type", research_paper_filters_dto_1.ResearchPaperFiltersDto)
], EntityTypeFiltersDto.prototype, "research_paper", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Podcast entities',
        type: podcast_filters_dto_1.PodcastFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => podcast_filters_dto_1.PodcastFiltersDto),
    __metadata("design:type", podcast_filters_dto_1.PodcastFiltersDto)
], EntityTypeFiltersDto.prototype, "podcast", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Community entities',
        type: community_filters_dto_1.CommunityFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => community_filters_dto_1.CommunityFiltersDto),
    __metadata("design:type", community_filters_dto_1.CommunityFiltersDto)
], EntityTypeFiltersDto.prototype, "community", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Grant entities',
        type: grant_filters_dto_1.GrantFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => grant_filters_dto_1.GrantFiltersDto),
    __metadata("design:type", grant_filters_dto_1.GrantFiltersDto)
], EntityTypeFiltersDto.prototype, "grant", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Newsletter entities',
        type: newsletter_filters_dto_1.NewsletterFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => newsletter_filters_dto_1.NewsletterFiltersDto),
    __metadata("design:type", newsletter_filters_dto_1.NewsletterFiltersDto)
], EntityTypeFiltersDto.prototype, "newsletter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Book entities',
        type: book_filters_dto_1.BookFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => book_filters_dto_1.BookFiltersDto),
    __metadata("design:type", book_filters_dto_1.BookFiltersDto)
], EntityTypeFiltersDto.prototype, "book", void 0);
//# sourceMappingURL=entity-type-filters.dto.js.map