"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HardwareFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class HardwareFiltersDto {
}
exports.HardwareFiltersDto = HardwareFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by hardware types',
        type: [String],
        example: ['GPU', 'CPU', 'FPGA', 'TPU', 'ASIC'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], HardwareFiltersDto.prototype, "hardware_types", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by manufacturers',
        type: [String],
        example: ['NVIDIA', 'Intel', 'AMD', 'Apple'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], HardwareFiltersDto.prototype, "manufacturers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by release date from (YYYY-MM-DD)',
        example: '2023-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], HardwareFiltersDto.prototype, "release_date_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by release date to (YYYY-MM-DD)',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], HardwareFiltersDto.prototype, "release_date_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in price range text (partial match)',
        example: '$500',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HardwareFiltersDto.prototype, "price_range", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum price (in dollars)',
        type: Number,
        minimum: 0,
        example: 500,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], HardwareFiltersDto.prototype, "price_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum price (in dollars)',
        type: Number,
        minimum: 0,
        example: 2000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], HardwareFiltersDto.prototype, "price_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in specifications (searches within the JSON object)',
        example: 'GDDR6',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HardwareFiltersDto.prototype, "specifications_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter hardware that has datasheet URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    __metadata("design:type", Boolean)
], HardwareFiltersDto.prototype, "has_datasheet", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by memory specifications (partial match)',
        example: '16GB',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HardwareFiltersDto.prototype, "memory_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by processor specifications (partial match)',
        example: 'Intel i9',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HardwareFiltersDto.prototype, "processor_search", void 0);
//# sourceMappingURL=hardware-filters.dto.js.map