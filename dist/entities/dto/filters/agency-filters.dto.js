"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgencyFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class AgencyFiltersDto {
}
exports.AgencyFiltersDto = AgencyFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by services offered (searches within the JSON array)',
        type: [String],
        example: ['AI Strategy', 'Machine Learning', 'Data Science', 'Automation'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AgencyFiltersDto.prototype, "services_offered", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by industry focus (searches within the JSON array)',
        type: [String],
        example: ['Healthcare', 'Finance', 'E-commerce', 'Manufacturing'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AgencyFiltersDto.prototype, "industry_focus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by target client size (searches within the JSON array)',
        type: [String],
        example: ['Startup', 'SMB', 'Enterprise', 'Fortune 500'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AgencyFiltersDto.prototype, "target_client_size", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by target audience (searches within the JSON array)',
        type: [String],
        example: ['CTOs', 'Data Scientists', 'Business Leaders', 'Developers'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AgencyFiltersDto.prototype, "target_audience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by location summary (partial match)',
        example: 'San Francisco',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgencyFiltersDto.prototype, "location_summary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter agencies that have portfolio URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AgencyFiltersDto.prototype, "has_portfolio", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in pricing information (partial match)',
        example: 'hourly rate',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgencyFiltersDto.prototype, "pricing_info_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in services offered (searches within the JSON array)',
        example: 'machine learning',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgencyFiltersDto.prototype, "services_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in industry focus (searches within the JSON array)',
        example: 'healthcare',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgencyFiltersDto.prototype, "industry_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in target audience (searches within the JSON array)',
        example: 'developers',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgencyFiltersDto.prototype, "audience_search", void 0);
//# sourceMappingURL=agency-filters.dto.js.map