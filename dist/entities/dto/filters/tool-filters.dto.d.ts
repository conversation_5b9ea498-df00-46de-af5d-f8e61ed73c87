import { TechnicalLevel, LearningCurve, PricingModel, PriceRange } from '@generated-prisma';
export declare class ToolFiltersDto {
    technical_levels?: TechnicalLevel[];
    learning_curves?: LearningCurve[];
    pricing_models?: PricingModel[];
    price_ranges?: PriceRange[];
    has_api?: boolean;
    has_free_tier?: boolean;
    open_source?: boolean;
    mobile_support?: boolean;
    demo_available?: boolean;
    platforms?: string[];
    integrations?: string[];
    frameworks?: string[];
    libraries?: string[];
    key_features_search?: string;
    use_cases_search?: string;
    target_audience_search?: string;
    deployment_options?: string[];
    support_channels?: string[];
    has_live_chat?: boolean;
    customization_level?: string;
    pricing_details_search?: string;
}
