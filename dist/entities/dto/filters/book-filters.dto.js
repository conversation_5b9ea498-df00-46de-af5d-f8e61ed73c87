"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class BookFiltersDto {
}
exports.BookFiltersDto = BookFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by author name (partial match)',
        example: 'Stuart Russell',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BookFiltersDto.prototype, "author", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by publisher (partial match)',
        example: 'MIT Press',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BookFiltersDto.prototype, "publisher", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by ISBN (partial match)',
        example: '978-0262039',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BookFiltersDto.prototype, "isbn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by book format',
        type: [String],
        example: ['Hardcover', 'Paperback', 'eBook', 'Audiobook'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], BookFiltersDto.prototype, "formats", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by publication date from (YYYY-MM-DD)',
        example: '2020-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], BookFiltersDto.prototype, "publication_date_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by publication date to (YYYY-MM-DD)',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], BookFiltersDto.prototype, "publication_date_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum page count',
        type: Number,
        minimum: 1,
        example: 200,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], BookFiltersDto.prototype, "page_count_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum page count',
        type: Number,
        minimum: 1,
        example: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], BookFiltersDto.prototype, "page_count_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter books that have purchase URL available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], BookFiltersDto.prototype, "has_purchase_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in book summary (partial match)',
        example: 'artificial intelligence',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BookFiltersDto.prototype, "summary_search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by format (partial match)',
        example: 'ebook',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BookFiltersDto.prototype, "format_search", void 0);
//# sourceMappingURL=book-filters.dto.js.map