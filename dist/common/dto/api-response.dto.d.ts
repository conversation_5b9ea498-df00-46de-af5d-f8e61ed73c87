export declare class ApiResponseDto<T> {
    success: boolean;
    data: T;
    constructor(data: T, success?: boolean);
    static success<T>(data: T): ApiResponseDto<T>;
    static error<T>(data: T): ApiResponseDto<T>;
}
export declare class ComprehensiveProfileApiResponseDto {
    success: boolean;
    data: {
        user: any;
        preferences: any;
        stats: any;
        recent_activity: any[];
    };
}
