"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComprehensiveProfileApiResponseDto = exports.ApiResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ApiResponseDto {
    constructor(data, success = true) {
        this.success = success;
        this.data = data;
    }
    static success(data) {
        return new ApiResponseDto(data, true);
    }
    static error(data) {
        return new ApiResponseDto(data, false);
    }
}
exports.ApiResponseDto = ApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indicates if the request was successful',
        example: true
    }),
    __metadata("design:type", Boolean)
], ApiResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The response data payload'
    }),
    __metadata("design:type", Object)
], ApiResponseDto.prototype, "data", void 0);
class ComprehensiveProfileApiResponseDto {
}
exports.ComprehensiveProfileApiResponseDto = ComprehensiveProfileApiResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indicates if the request was successful',
        example: true
    }),
    __metadata("design:type", Boolean)
], ComprehensiveProfileApiResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The comprehensive profile data'
    }),
    __metadata("design:type", Object)
], ComprehensiveProfileApiResponseDto.prototype, "data", void 0);
//# sourceMappingURL=api-response.dto.js.map